# Rate Negotiation Backend Documentation Plan

## 🎯 System Overview

Documentation for implementing a complete rate negotiation system that allows providers and organizations to negotiate rates within predefined ranges, with full history tracking and automated workflow management.

---

## 📊 Data Requirements

### **Core Data Models Needed**

#### **RateNegotiation**

**Purpose:** Main negotiation session between a provider and organization for a specific job

**Key Data:**

- Links to: Job, Provider, Organization
- Rate boundaries: minimum rate, maximum rate
- Current state: active offer amount, final agreed rate
- Status tracking: active, finalized, expired, cancelled
- Timeline: when started, when completed
- Settings: whether negotiation is still open

#### **RateOffer**

**Purpose:** Individual offers and counter-offers within a negotiation

**Key Data:**

- Links to: parent negotiation
- Offer details: proposed rate, offer type (initial/counter/final)
- Who made it: provider or organization identifier
- Status: pending, accepted, declined, expired, withdrawn
- Optional data: message with offer, expiration time, decline reason
- Timeline: when made, when responded to

#### **Job Updates**

**Purpose:** Extend existing job data to support negotiations

**Additional Data Needed:**

- Rate range: minimum and maximum acceptable rates
- Settings: whether rate negotiation is allowed
- Final outcome: agreed rate when negotiation completes

#### **Action/Event Tracking**

**Purpose:** Log all negotiation activities for audit and timeline

**Event Types to Track:**

- Negotiation started
- Offer submitted
- Offer accepted/declined
- Offer expired
- Negotiation finalized
- Negotiation cancelled

---

## 🔄 Core Functionality Requirements

### **1. Starting Negotiations**

**What it does:** Allow providers to begin rate discussions for a job

**Business Rules:**

- Only one negotiation per job-provider pair
- Job must allow negotiations
- User must have permission to negotiate
- Rate range must be defined on the job

**Process:**

1. Validate user can negotiate for this job
2. Check no existing negotiation exists
3. Get rate boundaries from job
4. Create negotiation record
5. Optionally create initial offer
6. Send notifications to relevant parties
7. Log the start event

### **2. Submitting Offers**

**What it does:** Allow either party to propose rates

**Business Rules:**

- Rate must be within job's defined range
- Only active negotiations can receive offers
- User must be part of the negotiation
- Offers can have expiration times

**Process:**

1. Validate user has permission
2. Check rate is within allowed range
3. Verify negotiation is still active
4. Create offer record
5. Update negotiation with current offer
6. Notify the other party
7. Log the offer event
8. Schedule expiration if needed

### **3. Responding to Offers**

**What it does:** Accept, decline, or counter-offer existing proposals

**Business Rules:**

- Can only respond to pending offers
- Response must come from the other party
- Accepting finalizes the negotiation
- Declining can include a counter-offer
- Must provide reason for declines

**Process:**

1. Validate user can respond
2. Check offer is still pending
3. If accepting: finalize entire negotiation
4. If declining: mark as declined, optionally create counter
5. Send response notifications
6. Log the response event
7. Update all related records

### **4. Finalizing Negotiations**

**What it does:** Lock in the agreed rate and close negotiation

**Business Rules:**

- Only happens when an offer is accepted
- Updates the original job with final rate
- Prevents further offers
- Triggers contract processes

**Process:**

1. Mark negotiation as finalized
2. Update job with agreed rate
3. Close all pending offers
4. Send completion notifications
5. Log finalization event
6. Trigger any follow-up processes

### **5. Automatic Processes**

**What it does:** Handle time-based and system events

**Offer Expiration:**

- Check for expired offers regularly
- Mark them as expired
- Notify relevant parties
- Log expiration events

**Stale Negotiations:**

- Find negotiations without recent activity
- Send reminder notifications
- Flag for review if needed

---

## 📡 API Endpoints Needed

### **Negotiation Management**

- **Create negotiation** - Start new rate discussion
- **Get negotiation details** - Retrieve negotiation info
- **Cancel negotiation** - End negotiation early
- **Get negotiation history** - Full timeline of events

### **Offer Management**

- **Submit offer** - Make rate proposals
- **Respond to offer** - Accept/decline with optional counter
- **Get active offers** - Current pending offers
- **Withdraw offer** - Cancel own pending offer

### **Query Operations**

- **Get job negotiations** - All negotiations for a job
- **Get provider negotiations** - All negotiations for a provider
- **Get organization negotiations** - All negotiations for an organization
- **Admin: Get all negotiations** - System-wide view with filters

### **Timeline & History**

- **Get negotiation timeline** - Chronological events
- **Get offer history** - All offers for a negotiation
- **Get merged timeline** - Offers, messages, and actions combined

---

## 🔔 Notification Requirements

### **Event-Based Notifications**

**When negotiation starts:**

- Notify organization that provider wants to negotiate
- Notify provider that negotiation was created

**When offer is submitted:**

- Notify the other party of new offer
- Include offer amount and any message
- Show expiration time if applicable

**When offer is responded to:**

- Notify original offerer of response
- If declined, include reason
- If accepted, notify of finalization

**When offers expire:**

- Notify both parties
- Suggest next steps

**When negotiation finalizes:**

- Notify both parties of agreed rate
- Confirm next steps in process

### **Reminder Notifications**

- Pending offers nearing expiration
- Stale negotiations needing attention
- Required responses waiting

---

## 🔒 Permission & Security Rules

### **Who Can Do What**

**Providers can:**

- Start negotiations for jobs they can apply to
- Submit offers in their own negotiations
- Respond to organization offers
- View their own negotiation history

**Organizations can:**

- Respond to provider-initiated negotiations
- Submit counter-offers
- Accept/decline provider offers
- View negotiations for their jobs

**Admins can:**

- View all negotiations
- Override rate ranges if needed
- Cancel problematic negotiations
- Access full audit logs

### **Data Access Rules**

- Users only see negotiations they're part of
- Negotiation details are private to participants
- Admin access is logged for audit
- Rate ranges are visible to all participants

---

## 📈 Timeline & History Features

### **Timeline Data Sources**

**Merge data from:**

- Negotiation events (start, finalize, cancel)
- Offer events (submit, accept, decline, expire)
- Message events (if messaging is integrated)
- System actions (automated processes)

### **Timeline Display Requirements**

- Chronological order of all events
- Clear visual distinction between event types
- Show who performed each action
- Include relevant details (rates, messages, etc.)
- Highlight current status and next steps

### **History Tracking**

- Complete audit trail of all changes
- Track rate progression over time
- Show decision points and outcomes
- Enable filtering by event type or date range

---

## 🎛️ Administrative Features

### **Monitoring & Oversight**

- View all active negotiations
- Filter by status, organization, provider
- See negotiation statistics and trends
- Identify stalled or problematic negotiations

### **Configuration Management**

- Set system-wide rate limits
- Configure notification settings
- Manage automatic expiration times
- Control who can initiate negotiations

### **Reporting Capabilities**

- Negotiation completion rates
- Average time to resolution
- Rate distribution analysis
- User engagement metrics

---

## 🔄 Integration Points

### **With Existing Systems**

**Job Posting System:**

- Read rate ranges from jobs
- Update jobs with final rates
- Check job negotiation settings

**User Management:**

- Validate user permissions
- Get user organization relationships
- Check provider eligibility

**Messaging System:**

- Create system messages for events
- Link negotiation events to conversations
- Coordinate with chat features

**Notification System:**

- Send email notifications
- Create in-app notifications
- Handle notification preferences

**Action/Event System:**

- Log all negotiation activities
- Integrate with existing audit trail
- Coordinate with other system events

---

## 📋 Business Logic Rules

### **Rate Validation**

- All offers must be within job's rate range
- Soft warnings for edge-case rates
- Hard stops for rates outside bounds
- Clear error messages for invalid rates

### **Workflow Rules**

- One negotiation per job-provider pair
- No offers on finalized negotiations
- Expiration times are enforced automatically
- Counter-offers reset expiration timers

### **Status Management**

- Clear status transitions and rules
- Prevent invalid state changes
- Automatic status updates where appropriate
- Manual overrides for admin users

### **Notification Rules**

- Immediate notifications for urgent events
- Digest options for less critical updates
- User preference controls
- Escalation for ignored notifications

---

## ✅ Testing & Validation Requirements

### **Functional Testing**

- Complete negotiation workflows
- All permission scenarios
- Rate validation edge cases
- Timeline data accuracy

### **Business Rule Testing**

- Boundary conditions for rates
- State transition validation
- Notification delivery
- Expiration handling

### **Integration Testing**

- Cross-system data consistency
- Performance under load
- Concurrent negotiation handling
- Error recovery scenarios

### **User Experience Testing**

- Intuitive workflow progression
- Clear status communication
- Responsive notification system
- Mobile-friendly interfaces

---

## 🚀 Implementation Phases

### **Phase 1: Foundation**

- Set up core data models
- Implement basic CRUD operations
- Add permission checking
- Create simple UI for testing

### **Phase 2: Core Workflows**

- Build negotiation start process
- Implement offer submission
- Add response handling
- Create basic notifications

### **Phase 3: Advanced Features**

- Add expiration handling
- Build timeline functionality
- Implement admin features
- Enhance notification system

### **Phase 4: Polish & Integration**

- Optimize performance
- Complete UI implementation
- Add comprehensive testing
- Integrate with existing systems

This documentation provides the complete functional requirements without implementation specifics, allowing your team to build the system according to your preferred technologies and patterns.
