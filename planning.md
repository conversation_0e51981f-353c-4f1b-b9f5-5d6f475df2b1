# Rate Negotiation Backend Documentation Plan

## 🎯 System Overview

Documentation for implementing a complete rate negotiation system that allows providers and organizations to negotiate rates within predefined ranges, with full history tracking and automated workflow management.

---

## 📊 Data Requirements

### **Core Data Models Needed**

#### **RateNegotiation**

**Purpose:** Main negotiation session between a provider and organization for a specific job

**Key Data:**

- Links to: Job, Provider, Organization
- Rate boundaries: minimum rate, maximum rate
- Current state: active offer amount, final agreed rate
- Status tracking: active, finalized, expired, cancelled
- Timeline: when started, when completed
- Settings: whether negotiation is still open

**Database Schema (add to `packages/db-medical/prisma/schema.prisma`):**

```prisma
enum RateNegotiationStatus {
  ACTIVE
  FINALIZED
  EXPIRED
  CANCELLED

  @@schema("public")
}

model RateNegotiation {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  status           RateNegotiationStatus @default(ACTIVE)
  minRate          Float
  maxRate          Float
  currentOfferRate Float?
  finalAgreedRate  Float?
  is<PERSON><PERSON>               @default(true)

  // Relations - following existing patterns from JobPost/Offer models
  job            JobPost      @relation(fields: [jobId], references: [id])
  jobId          String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  provider       Provider     @relation(fields: [providerId], references: [id])
  providerId     String

  offers  RateOffer[]
  actions Action[]

  @@unique([jobId, providerId]) // One negotiation per job-provider pair
  @@index([status, organizationId, providerId, jobId], name: "rate_negotiation_idx")
  @@schema("public")
}
```

**Related Files:**

- `packages/db-medical/prisma/schema.prisma` - Add new models
- `packages/api-medical/src/router/negotiations/` - New router directory

#### **RateOffer**

**Purpose:** Individual offers and counter-offers within a negotiation

**Key Data:**

- Links to: parent negotiation
- Offer details: proposed rate, offer type (initial/counter/final)
- Who made it: provider or organization identifier
- Status: pending, accepted, declined, expired, withdrawn
- Optional data: message with offer, expiration time, decline reason
- Timeline: when made, when responded to

**Database Schema (add to `packages/db-medical/prisma/schema.prisma`):**

```prisma
enum RateOfferStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
  WITHDRAWN

  @@schema("public")
}

enum RateOfferType {
  INITIAL
  COUNTER
  FINAL

  @@schema("public")
}

enum RateOfferMadeBy {
  PROVIDER
  ORGANIZATION

  @@schema("public")
}

model RateOffer {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  proposedRate  Float
  type          RateOfferType   @default(INITIAL)
  status        RateOfferStatus @default(PENDING)
  madeBy        RateOfferMadeBy
  message       String?
  declineReason String?
  expiresAt     DateTime?
  respondedAt   DateTime?

  // Relations - following existing Offer model patterns
  negotiation   RateNegotiation @relation(fields: [negotiationId], references: [id])
  negotiationId String

  actions Action[]

  @@index([status, negotiationId, madeBy], name: "rate_offer_idx")
  @@schema("public")
}
```

**Related Files:**

- Similar to existing `packages/api-medical/src/router/jobs/offers/offers.ts` patterns

#### **Job Updates**

**Purpose:** Extend existing job data to support negotiations

**Additional Data Needed:**

- Rate range: minimum and maximum acceptable rates
- Settings: whether rate negotiation is allowed
- Final outcome: agreed rate when negotiation completes

**Database Schema Updates (modify existing JobPost in `packages/db-medical/prisma/schema.prisma`):**

```prisma
model JobPost {
  // ... existing fields ...

  // Rate negotiation fields
  allowRateNegotiation Boolean @default(false)
  minNegotiableRate    Float?
  maxNegotiableRate    Float?
  finalNegotiatedRate  Float?

  // Relations
  rateNegotiations RateNegotiation[]

  // ... existing relations ...
}
```

**Related Files:**

- `packages/api-medical/src/router/jobs/jobs.ts` - Update job selection and mutations
- `packages/db-medical/prisma/schema.prisma` - Extend JobPost model

#### **Action/Event Tracking**

**Purpose:** Log all negotiation activities for audit and timeline

**Event Types to Track:**

- Negotiation started
- Offer submitted
- Offer accepted/declined
- Offer expired
- Negotiation finalized
- Negotiation cancelled

**Action Constants (add to `packages/api-medical/src/constants/actions.ts`):**

```typescript
// Add to existing ActionType enum
export enum ActionType {
  // ... existing actions ...

  // Rate negotiation actions
  START_RATE_NEGOTIATION = "START_RATE_NEGOTIATION",
  SUBMIT_RATE_OFFER = "SUBMIT_RATE_OFFER",
  ACCEPT_RATE_OFFER = "ACCEPT_RATE_OFFER",
  DECLINE_RATE_OFFER = "DECLINE_RATE_OFFER",
  WITHDRAW_RATE_OFFER = "WITHDRAW_RATE_OFFER",
  EXPIRE_RATE_OFFER = "EXPIRE_RATE_OFFER",
  FINALIZE_RATE_NEGOTIATION = "FINALIZE_RATE_NEGOTIATION",
  CANCEL_RATE_NEGOTIATION = "CANCEL_RATE_NEGOTIATION",
}

// Add to existing ResourceType enum
export enum ResourceType {
  // ... existing resources ...
  RATE_NEGOTIATION = "RATE_NEGOTIATION",
  RATE_OFFER = "RATE_OFFER",
}
```

**Related Files:**

- `packages/api-medical/src/constants/actions.ts` - Add new action types
- `packages/api-medical/src/lib/actions/` - Create negotiation action handlers
- `packages/api-medical/src/router/actions.ts` - Existing action logging system

---

## 🔄 Core Functionality Requirements

### **1. Starting Negotiations**

**What it does:** Allow providers to begin rate discussions for a job

**Business Rules:**

- Only one negotiation per job-provider pair
- Job must allow negotiations
- User must have permission to negotiate
- Rate range must be defined on the job

**Process:**

1. Validate user can negotiate for this job
2. Check no existing negotiation exists
3. Get rate boundaries from job
4. Create negotiation record
5. Optionally create initial offer
6. Send notifications to relevant parties
7. Log the start event

**API Implementation (create `packages/api-medical/src/router/negotiations/negotiations.ts`):**

```typescript
// Following patterns from packages/api-medical/src/router/jobs/offers/offers.ts
export const negotiationsRouter = createTRPCRouter({
  start: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        initialOfferRate: z.number().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Validate permissions (similar to existing offer validation)
      const job = await ctx.prisma.jobPost.findUnique({
        where: { id: input.jobId },
        select: {
          allowRateNegotiation: true,
          minNegotiableRate: true,
          maxNegotiableRate: true,
          organizationId: true,
        },
      });

      if (!job?.allowRateNegotiation) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Rate negotiation not allowed for this job",
        });
      }

      // 2. Check for existing negotiation
      const existing = await ctx.prisma.rateNegotiation.findUnique({
        where: {
          jobId_providerId: {
            jobId: input.jobId,
            providerId: ctx.user.providerId,
          },
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Negotiation already exists for this job",
        });
      }

      // 3. Create negotiation and log action
      const negotiation = await ctx.prisma.rateNegotiation.create({
        data: {
          jobId: input.jobId,
          providerId: ctx.user.providerId,
          organizationId: job.organizationId,
          minRate: job.minNegotiableRate!,
          maxRate: job.maxNegotiableRate!,
        },
      });

      // 4. Log action (following existing patterns)
      await performAction({
        type: ActionType.START_RATE_NEGOTIATION,
        resourceType: ResourceType.RATE_NEGOTIATION,
        resourceId: negotiation.id,
        actorId: ctx.session.userId,
        organizationId: job.organizationId,
        providerId: ctx.user.providerId,
      });

      return negotiation;
    }),
});
```

**Related Files:**

- `packages/api-medical/src/router/negotiations/` - New router directory
- `packages/api-medical/src/lib/actions/` - Action handlers for notifications

### **2. Submitting Offers**

**What it does:** Allow either party to propose rates

**Business Rules:**

- Rate must be within job's defined range
- Only active negotiations can receive offers
- User must be part of the negotiation
- Offers can have expiration times

**Process:**

1. Validate user has permission
2. Check rate is within allowed range
3. Verify negotiation is still active
4. Create offer record
5. Update negotiation with current offer
6. Notify the other party
7. Log the offer event
8. Schedule expiration if needed

**API Implementation (add to `packages/api-medical/src/router/negotiations/offers.ts`):**

```typescript
// Following patterns from packages/api-medical/src/router/jobs/offers/offers.ts
export const rateOffersRouter = createTRPCRouter({
  submit: protectedProcedure
    .input(
      z.object({
        negotiationId: z.string(),
        proposedRate: z.number(),
        message: z.string().optional(),
        expiresAt: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Get negotiation and validate permissions
      const negotiation = await ctx.prisma.rateNegotiation.findUnique({
        where: { id: input.negotiationId },
        include: { job: true, provider: true, organization: true },
      });

      if (!negotiation || negotiation.status !== "ACTIVE") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Negotiation not found or not active",
        });
      }

      // 2. Validate rate is within bounds
      if (
        input.proposedRate < negotiation.minRate ||
        input.proposedRate > negotiation.maxRate
      ) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Rate must be between ${negotiation.minRate} and ${negotiation.maxRate}`,
        });
      }

      // 3. Determine who is making the offer
      const madeBy =
        ctx.user.providerId === negotiation.providerId
          ? "PROVIDER"
          : "ORGANIZATION";

      // 4. Create offer and update negotiation
      const offer = await ctx.prisma.rateOffer.create({
        data: {
          negotiationId: input.negotiationId,
          proposedRate: input.proposedRate,
          madeBy,
          message: input.message,
          expiresAt: input.expiresAt,
        },
      });

      // 5. Update negotiation current offer
      await ctx.prisma.rateNegotiation.update({
        where: { id: input.negotiationId },
        data: { currentOfferRate: input.proposedRate },
      });

      // 6. Log action (following existing action patterns)
      await performAction({
        type: ActionType.SUBMIT_RATE_OFFER,
        resourceType: ResourceType.RATE_OFFER,
        resourceId: offer.id,
        actorId: ctx.session.userId,
        organizationId: negotiation.organizationId,
        providerId: negotiation.providerId,
        metadata: { proposedRate: input.proposedRate, madeBy },
      });

      return offer;
    }),
});
```

**Related Files:**

- `packages/api-medical/src/router/negotiations/offers.ts` - Rate offer operations
- `packages/api-medical/src/lib/actions/handlers/rateOffer.ts` - Notification handlers

### **3. Responding to Offers**

**What it does:** Accept, decline, or counter-offer existing proposals

**Business Rules:**

- Can only respond to pending offers
- Response must come from the other party
- Accepting finalizes the negotiation
- Declining can include a counter-offer
- Must provide reason for declines

**Process:**

1. Validate user can respond
2. Check offer is still pending
3. If accepting: finalize entire negotiation
4. If declining: mark as declined, optionally create counter
5. Send response notifications
6. Log the response event
7. Update all related records

### **4. Finalizing Negotiations**

**What it does:** Lock in the agreed rate and close negotiation

**Business Rules:**

- Only happens when an offer is accepted
- Updates the original job with final rate
- Prevents further offers
- Triggers contract processes

**Process:**

1. Mark negotiation as finalized
2. Update job with agreed rate
3. Close all pending offers
4. Send completion notifications
5. Log finalization event
6. Trigger any follow-up processes

### **5. Automatic Processes**

**What it does:** Handle time-based and system events

**Offer Expiration:**

- Check for expired offers regularly
- Mark them as expired
- Notify relevant parties
- Log expiration events

**Stale Negotiations:**

- Find negotiations without recent activity
- Send reminder notifications
- Flag for review if needed

---

## 📡 API Endpoints Needed

### **Negotiation Management**

- **Create negotiation** - Start new rate discussion
- **Get negotiation details** - Retrieve negotiation info
- **Cancel negotiation** - End negotiation early
- **Get negotiation history** - Full timeline of events

### **Offer Management**

- **Submit offer** - Make rate proposals
- **Respond to offer** - Accept/decline with optional counter
- **Get active offers** - Current pending offers
- **Withdraw offer** - Cancel own pending offer

### **Query Operations**

- **Get job negotiations** - All negotiations for a job
- **Get provider negotiations** - All negotiations for a provider
- **Get organization negotiations** - All negotiations for an organization
- **Admin: Get all negotiations** - System-wide view with filters

### **Timeline & History**

- **Get negotiation timeline** - Chronological events
- **Get offer history** - All offers for a negotiation
- **Get merged timeline** - Offers, messages, and actions combined

**Router Structure (create `packages/api-medical/src/router/negotiations/index.ts`):**

```typescript
// Following patterns from packages/api-medical/src/router/jobs/index.ts
import { mergeRouters } from "@trpc/server/unstable-core-do-not-import";

import { createTRPCRouter } from "../../trpc";
import { negotiationsRouter } from "./negotiations";
import { rateOffersRouter } from "./offers";
import { negotiationTimelineRouter } from "./timeline";

export const rateNegotiationsRouter = mergeRouters(
  negotiationsRouter,
  createTRPCRouter({
    offers: rateOffersRouter,
    timeline: negotiationTimelineRouter,
  }),
);

// Add to main router in packages/api-medical/src/router/index.ts:
// rateNegotiations: rateNegotiationsRouter,
```

**Query Examples (add to negotiations router):**

```typescript
// Get negotiations for a job
getByJob: protectedProcedure
  .input(z.object({ jobId: z.string() }))
  .query(async ({ ctx, input }) => {
    return ctx.prisma.rateNegotiation.findMany({
      where: { jobId: input.jobId },
      include: {
        provider: { include: { person: true } },
        offers: { orderBy: { createdAt: 'desc' } },
      },
    });
  }),

// Get negotiations for a provider
getByProvider: protectedProcedure
  .input(z.object({ providerId: z.string() }))
  .query(async ({ ctx, input }) => {
    return ctx.prisma.rateNegotiation.findMany({
      where: { providerId: input.providerId },
      include: {
        job: { include: { organization: true } },
        offers: { orderBy: { createdAt: 'desc' } },
      },
    });
  }),
```

**Related Files:**

- `packages/api-medical/src/router/negotiations/index.ts` - Main router
- `packages/api-medical/src/router/negotiations/negotiations.ts` - Core operations
- `packages/api-medical/src/router/negotiations/offers.ts` - Offer management
- `packages/api-medical/src/router/negotiations/timeline.ts` - History/timeline

---

## 🔔 Notification Requirements

### **Event-Based Notifications**

**When negotiation starts:**

- Notify organization that provider wants to negotiate
- Notify provider that negotiation was created

**When offer is submitted:**

- Notify the other party of new offer
- Include offer amount and any message
- Show expiration time if applicable

**When offer is responded to:**

- Notify original offerer of response
- If declined, include reason
- If accepted, notify of finalization

**When offers expire:**

- Notify both parties
- Suggest next steps

**When negotiation finalizes:**

- Notify both parties of agreed rate
- Confirm next steps in process

### **Reminder Notifications**

- Pending offers nearing expiration
- Stale negotiations needing attention
- Required responses waiting

**Notification Implementation (create `packages/api-medical/src/lib/actions/handlers/rateNegotiation.ts`):**

```typescript
// Following patterns from packages/api-medical/src/lib/actions/actionHandler.ts
import type { ActionLogicFunction } from "../actionHandler";

import { ActionType } from "../../../constants/actions";

export const rateNegotiationActionLogic: ActionLogicFunction = async (
  payload,
  collectors,
) => {
  switch (payload.type) {
    case ActionType.START_RATE_NEGOTIATION:
      // Notify organization about new negotiation request
      collectors.organizationNotifications.push({
        organizationId: payload.organizationId!,
        subject: "New Rate Negotiation Request",
        email: `A provider has requested to negotiate rates for job ${payload.metadata?.jobTitle}`,
        text: `Rate negotiation started for ${payload.metadata?.jobTitle}`,
        topic: "jobs",
      });
      break;

    case ActionType.SUBMIT_RATE_OFFER:
      // Notify the other party about new offer
      if (payload.metadata?.madeBy === "PROVIDER") {
        collectors.organizationNotifications.push({
          organizationId: payload.organizationId!,
          subject: "New Rate Offer Received",
          email: `Provider has submitted a rate offer of $${payload.metadata?.proposedRate}`,
          text: `New rate offer: $${payload.metadata?.proposedRate}`,
          topic: "jobs",
        });
      } else {
        collectors.providerNotifications.push({
          providerId: payload.providerId!,
          subject: "New Rate Offer Received",
          email: `Organization has submitted a rate offer of $${payload.metadata?.proposedRate}`,
          text: `New rate offer: $${payload.metadata?.proposedRate}`,
          topic: "jobs",
        });
      }
      break;

    case ActionType.FINALIZE_RATE_NEGOTIATION:
      // Notify both parties of finalization
      collectors.organizationNotifications.push({
        organizationId: payload.organizationId!,
        subject: "Rate Negotiation Finalized",
        email: `Rate negotiation completed at $${payload.metadata?.finalRate}`,
        text: `Final rate agreed: $${payload.metadata?.finalRate}`,
        topic: "jobs",
      });

      collectors.providerNotifications.push({
        providerId: payload.providerId!,
        subject: "Rate Negotiation Finalized",
        email: `Rate negotiation completed at $${payload.metadata?.finalRate}`,
        text: `Final rate agreed: $${payload.metadata?.finalRate}`,
        topic: "jobs",
      });
      break;
  }
};
```

**Related Files:**

- `packages/api-medical/src/lib/actions/handlers/rateNegotiation.ts` - Notification logic
- `packages/api-medical/src/lib/notifications/send.ts` - Existing notification system
- `packages/api-medical/src/jobs/notifications/process-action-event.ts` - QStash integration

---

## 🔒 Permission & Security Rules

### **Who Can Do What**

**Providers can:**

- Start negotiations for jobs they can apply to
- Submit offers in their own negotiations
- Respond to organization offers
- View their own negotiation history

**Organizations can:**

- Respond to provider-initiated negotiations
- Submit counter-offers
- Accept/decline provider offers
- View negotiations for their jobs

**Admins can:**

- View all negotiations
- Override rate ranges if needed
- Cancel problematic negotiations
- Access full audit logs

### **Data Access Rules**

- Users only see negotiations they're part of
- Negotiation details are private to participants
- Admin access is logged for audit
- Rate ranges are visible to all participants

---

## 📈 Timeline & History Features

### **Timeline Data Sources**

**Merge data from:**

- Negotiation events (start, finalize, cancel)
- Offer events (submit, accept, decline, expire)
- Message events (if messaging is integrated)
- System actions (automated processes)

### **Timeline Display Requirements**

- Chronological order of all events
- Clear visual distinction between event types
- Show who performed each action
- Include relevant details (rates, messages, etc.)
- Highlight current status and next steps

### **History Tracking**

- Complete audit trail of all changes
- Track rate progression over time
- Show decision points and outcomes
- Enable filtering by event type or date range

**Timeline Implementation (create `packages/api-medical/src/router/negotiations/timeline.ts`):**

```typescript
// Following patterns from existing action queries
export const negotiationTimelineRouter = createTRPCRouter({
  getTimeline: protectedProcedure
    .input(
      z.object({
        negotiationId: z.string(),
        includeMessages: z.boolean().default(false),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Get all actions related to this negotiation
      const actions = await ctx.prisma.action.findMany({
        where: {
          OR: [
            { rateNegotiationId: input.negotiationId },
            {
              rateOfferId: {
                in: await ctx.prisma.rateOffer
                  .findMany({
                    where: { negotiationId: input.negotiationId },
                    select: { id: true },
                  })
                  .then((offers) => offers.map((o) => o.id)),
              },
            },
          ],
        },
        include: {
          actor: { select: { firstName: true, lastName: true, role: true } },
        },
        orderBy: { createdAt: "asc" },
      });

      // Get all offers for this negotiation
      const offers = await ctx.prisma.rateOffer.findMany({
        where: { negotiationId: input.negotiationId },
        orderBy: { createdAt: "asc" },
      });

      // Merge and sort timeline events
      const timeline = [
        ...actions.map((action) => ({
          type: "action" as const,
          id: action.id,
          createdAt: action.createdAt,
          actionType: action.type,
          actor: action.actor,
          metadata: action.metadata,
        })),
        ...offers.map((offer) => ({
          type: "offer" as const,
          id: offer.id,
          createdAt: offer.createdAt,
          proposedRate: offer.proposedRate,
          status: offer.status,
          madeBy: offer.madeBy,
          message: offer.message,
        })),
      ].sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());

      return timeline;
    }),

  getOfferHistory: protectedProcedure
    .input(z.object({ negotiationId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.rateOffer.findMany({
        where: { negotiationId: input.negotiationId },
        orderBy: { createdAt: "desc" },
        include: {
          actions: {
            include: { actor: { select: { firstName: true, lastName: true } } },
          },
        },
      });
    }),
});
```

**Related Files:**

- `packages/api-medical/src/router/negotiations/timeline.ts` - Timeline queries
- `packages/api-medical/src/router/actions.ts` - Existing action system patterns

---

## 🎛️ Administrative Features

### **Monitoring & Oversight**

- View all active negotiations
- Filter by status, organization, provider
- See negotiation statistics and trends
- Identify stalled or problematic negotiations

### **Configuration Management**

- Set system-wide rate limits
- Configure notification settings
- Manage automatic expiration times
- Control who can initiate negotiations

### **Reporting Capabilities**

- Negotiation completion rates
- Average time to resolution
- Rate distribution analysis
- User engagement metrics

---

## 🔄 Integration Points

### **With Existing Systems**

**Job Posting System:**

- Read rate ranges from jobs
- Update jobs with final rates
- Check job negotiation settings

**User Management:**

- Validate user permissions
- Get user organization relationships
- Check provider eligibility

**Messaging System:**

- Create system messages for events
- Link negotiation events to conversations
- Coordinate with chat features

**Notification System:**

- Send email notifications
- Create in-app notifications
- Handle notification preferences

**Action/Event System:**

- Log all negotiation activities
- Integrate with existing audit trail
- Coordinate with other system events

**Integration Implementation Examples:**

```typescript
// Job Integration (extend packages/api-medical/src/router/jobs/jobs.ts)
const jobWithNegotiationData = {
  ...existingJobSelection,
  allowRateNegotiation: true,
  minNegotiableRate: true,
  maxNegotiableRate: true,
  finalNegotiatedRate: true,
  rateNegotiations: {
    include: {
      provider: { include: { person: true } },
      offers: { orderBy: { createdAt: "desc" }, take: 1 },
    },
  },
};

// Message Integration (extend packages/api-medical/src/router/messages/messages.ts)
// Add "rate_negotiation" to zMessageResourceEnum
export const zMessageResourceEnum = z.enum([
  "shift",
  "application",
  "offer",
  "job",
  "position",
  "incident",
  "contract",
  "rate_negotiation", // Add this
]);

// User Permission Integration (create helper function)
async function validateNegotiationPermissions(
  ctx: Context,
  negotiationId: string,
  action: "read" | "write",
) {
  const negotiation = await ctx.prisma.rateNegotiation.findUnique({
    where: { id: negotiationId },
    select: { providerId: true, organizationId: true },
  });

  const isProvider = ctx.user.providerId === negotiation?.providerId;
  const isOrgMember = ctx.user.organizationId === negotiation?.organizationId;

  if (!isProvider && !isOrgMember && ctx.user.role !== "ADMIN") {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Not authorized to access this negotiation",
    });
  }

  return { isProvider, isOrgMember, negotiation };
}
```

**Related Files:**

- `packages/api-medical/src/router/jobs/jobs.ts` - Job integration
- `packages/api-medical/src/router/messages/messages.ts` - Message integration
- `packages/api-medical/src/lib/permissions/` - Permission helpers
- `packages/api-medical/src/lib/actions/` - Action system integration

---

## 📋 Business Logic Rules

### **Rate Validation**

- All offers must be within job's rate range
- Soft warnings for edge-case rates
- Hard stops for rates outside bounds
- Clear error messages for invalid rates

### **Workflow Rules**

- One negotiation per job-provider pair
- No offers on finalized negotiations
- Expiration times are enforced automatically
- Counter-offers reset expiration timers

### **Status Management**

- Clear status transitions and rules
- Prevent invalid state changes
- Automatic status updates where appropriate
- Manual overrides for admin users

### **Notification Rules**

- Immediate notifications for urgent events
- Digest options for less critical updates
- User preference controls
- Escalation for ignored notifications

---

## ✅ Testing & Validation Requirements

### **Functional Testing**

- Complete negotiation workflows
- All permission scenarios
- Rate validation edge cases
- Timeline data accuracy

### **Business Rule Testing**

- Boundary conditions for rates
- State transition validation
- Notification delivery
- Expiration handling

### **Integration Testing**

- Cross-system data consistency
- Performance under load
- Concurrent negotiation handling
- Error recovery scenarios

### **User Experience Testing**

- Intuitive workflow progression
- Clear status communication
- Responsive notification system
- Mobile-friendly interfaces

---

## 🚀 Implementation Phases

### **Phase 1: Foundation**

- Set up core data models
- Implement basic CRUD operations
- Add permission checking
- Create simple UI for testing

### **Phase 2: Core Workflows**

- Build negotiation start process
- Implement offer submission
- Add response handling
- Create basic notifications

### **Phase 3: Advanced Features**

- Add expiration handling
- Build timeline functionality
- Implement admin features
- Enhance notification system

### **Phase 4: Polish & Integration**

- Optimize performance
- Complete UI implementation
- Add comprehensive testing
- Integrate with existing systems

**Implementation Checklist:**

**Phase 1 Tasks:**

```typescript
// 1. Database Schema
// - Add RateNegotiation, RateOffer models to schema.prisma
// - Add negotiation fields to JobPost model
// - Run prisma migrate dev

// 2. API Foundation
// - Create packages/api-medical/src/router/negotiations/ directory
// - Implement basic CRUD operations following existing patterns
// - Add action types to constants/actions.ts

// 3. Permission System
// - Create validateNegotiationPermissions helper
// - Integrate with existing auth middleware
// - Test permission boundaries
```

**Phase 2 Tasks:**

```typescript
// 1. Core Workflows
// - Implement negotiation.start mutation
// - Implement offers.submit mutation
// - Implement offers.respond mutation
// - Add basic action logging

// 2. Notification Setup
// - Create rateNegotiation action handler
// - Integrate with existing QStash system
// - Test notification delivery

// 3. Basic Queries
// - Implement getByJob, getByProvider queries
// - Add timeline.getTimeline query
// - Test data retrieval
```

**Related Files for Implementation:**

- `packages/db-medical/prisma/schema.prisma` - Database models
- `packages/api-medical/src/router/negotiations/` - API implementation
- `packages/api-medical/src/constants/actions.ts` - Action types
- `packages/api-medical/src/lib/actions/handlers/` - Notification handlers
- `apps/web-med/src/components/negotiations/` - UI components (future)
