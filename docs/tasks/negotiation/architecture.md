# Match-Based Workflow Architecture

## Core Vision

**Match-centric workflow** replacing Application/Offer/JobPosition with **JobPost → Match → Contract** path.

**Principles:**

- Match as central orchestrator
- Bidirectional initiation (Provider/Organization)
- Multi-step verification + compensation negotiation
- Gradual migration from legacy workflow

---

## API Architecture & Integration Points

### **Core System Integration**

```mermaid
---
title: API Integration Architecture
---
graph TD
    A[Match API] --> B[Rate Negotiation Step]
    B --> C[Messages API - Existing]
    B --> D[Actions API - Existing]
    B --> E[Contracts API - Existing]

    F[JobCompensation] --> G[Shift Creation - Existing]
    F --> H[Payment Processing - Existing]

    B --> I[MatchStep.metadata JSON]
    I --> J[Rate Offer History]
    I --> K[Negotiation State]
    I --> L[Expiration Tracking]
```

### **API Structure Overview**

**New APIs to Build:**

- `matches.*` - Match workflow management
- `matches.steps.*` - Step progression and management
- `matches.compensation.*` - Rate negotiation within match steps

**Existing APIs to Extend:**

- `messages.*` - Already handles match-based messaging
- `actions.*` - Already handles audit trails and notifications
- `contracts.*` - Already handles contract creation and management

**Existing APIs to Reference:**

- `jobs.*` - Source of rate boundaries and job details
- `providers.*` - Participant validation and permissions
- `organizations.*` - Participant validation and permissions

### **Rate Negotiation Implementation Strategy**

**Application-Level Implementation using MatchStep.metadata:**

```typescript
// Rate negotiation state stored in MatchStep.metadata
interface RateNegotiationMetadata {
  // Current negotiation state
  currentOfferRate: number;
  lastOfferBy: "PROVIDER" | "ORGANIZATION";
  lastOfferAt: string; // ISO datetime
  offerExpiresAt?: string; // ISO datetime

  // Offer history (application-level tracking)
  offerHistory: RateOffer[];

  // Negotiation configuration
  strategy: "conservative" | "competitive" | "balanced" | "premium";
  allowCounterOffers: boolean;
  maxNegotiationRounds: number;
  currentRound: number;

  // Integration points
  threadId?: string; // For messaging
  compensationId: string; // JobCompensation record
}

interface RateOffer {
  id: string;
  proposedRate: number;
  madeBy: "PROVIDER" | "ORGANIZATION";
  madeAt: string; // ISO datetime
  message?: string;
  expiresAt?: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED" | "EXPIRED" | "WITHDRAWN";
  declineReason?: string;
  respondedAt?: string;
}
```

---

## API Endpoint Structure

### **Match Management APIs**

#### **Core Match Operations**

```typescript
// packages/api-medical/src/router/matches/matches.ts
export const matchesRouter = createTRPCRouter({
  // Match lifecycle
  create: protectedProcedure, // Create new match
  get: protectedProcedure, // Get match details
  list: protectedProcedure, // List matches with filters
  updateStatus: protectedProcedure, // Update match status

  // Step management
  steps: createTRPCRouter({
    list: protectedProcedure, // Get all steps for match
    get: protectedProcedure, // Get specific step
    start: protectedProcedure, // Start a step
    complete: protectedProcedure, // Mark step complete
    updateMetadata: protectedProcedure, // Update step metadata
  }),

  // Rate negotiation (specialized step operations)
  rateNegotiation: createTRPCRouter({
    start: protectedProcedure, // Initialize rate negotiation step
    submitOffer: protectedProcedure, // Submit rate offer
    respondToOffer: protectedProcedure, // Accept/decline/counter
    getHistory: protectedProcedure, // Get negotiation timeline
    finalize: protectedProcedure, // Finalize agreed rate
  }),
});
```

#### **Integration with Existing Systems**

**Messages Integration:**

```typescript
// Extend packages/api-medical/src/router/messages/messages.ts
// Messages already support match-based threads via matchId
// No changes needed - existing functionality works

// Example usage:
const thread = await ctx.prisma.thread.findUnique({
  where: { matchId: input.matchId },
});
```

**Actions Integration:**

```typescript
// Extend packages/api-medical/src/constants/actions.ts
export enum ActionType {
  // ... existing actions ...

  // Match actions
  CREATE_MATCH = "CREATE_MATCH",
  UPDATE_MATCH_STATUS = "UPDATE_MATCH_STATUS",

  // Match step actions
  START_MATCH_STEP = "START_MATCH_STEP",
  COMPLETE_MATCH_STEP = "COMPLETE_MATCH_STEP",
  UPDATE_STEP_METADATA = "UPDATE_STEP_METADATA",

  // Rate negotiation actions (specific step actions)
  START_RATE_NEGOTIATION = "START_RATE_NEGOTIATION",
  SUBMIT_RATE_OFFER = "SUBMIT_RATE_OFFER",
  ACCEPT_RATE_OFFER = "ACCEPT_RATE_OFFER",
  DECLINE_RATE_OFFER = "DECLINE_RATE_OFFER",
  COUNTER_RATE_OFFER = "COUNTER_RATE_OFFER",
  EXPIRE_RATE_OFFER = "EXPIRE_RATE_OFFER",
  FINALIZE_RATE_NEGOTIATION = "FINALIZE_RATE_NEGOTIATION",
}
```

**Contracts Integration:**

```typescript
// Extend packages/api-medical/src/router/contracts/contracts.ts
// Add match-based contract creation
createFromMatch: protectedProcedure
  .input(z.object({ matchId: z.string() }))
  .mutation(async ({ ctx, input }) => {
    // Get finalized match and compensation
    // Create contract using agreed rate
    // Link contract to match
  });
```

---

## Rate Negotiation Workflow Implementation

### **Step 1: Initialize Rate Negotiation**

```typescript
// API: matches.rateNegotiation.start
async function startRateNegotiation({ matchId, initialRate? }: {
  matchId: string;
  initialRate?: number;
}) {
  // 1. Get match and validate state
  const match = await getMatchWithValidation(matchId);

  // 2. Create or find RATE_NEGOTIATION step
  const step = await createOrGetRateNegotiationStep(matchId);

  // 3. Create JobCompensation record
  const compensation = await createJobCompensation({
    matchId,
    jobId: match.jobId,
    minRate: match.job.minNegotiableRate,
    maxRate: match.job.maxNegotiableRate,
    currentOfferRate: initialRate,
    rateStrategy: determineStrategy(match.initiator),
  });

  // 4. Initialize step metadata
  const metadata: RateNegotiationMetadata = {
    currentOfferRate: initialRate || compensation.minRate,
    lastOfferBy: match.initiator === "PROVIDER" ? "PROVIDER" : "ORGANIZATION",
    lastOfferAt: new Date().toISOString(),
    offerHistory: [],
    strategy: compensation.rateStrategy,
    allowCounterOffers: true,
    maxNegotiationRounds: 5,
    currentRound: 1,
    compensationId: compensation.id,
  };

  // 5. Update step with metadata
  await updateStepMetadata(step.id, metadata);

  // 6. Log action and send notifications
  await logAction("START_RATE_NEGOTIATION", { matchId, stepId: step.id });

  return { step, compensation, metadata };
}
```

### **Step 2: Submit Rate Offers**

```typescript
// API: matches.rateNegotiation.submitOffer
async function submitRateOffer({
  matchId,
  proposedRate,
  message?,
  expiresAt?
}: {
  matchId: string;
  proposedRate: number;
  message?: string;
  expiresAt?: Date;
}) {
  // 1. Get current step and metadata
  const step = await getRateNegotiationStep(matchId);
  const metadata = step.metadata as RateNegotiationMetadata;

  // 2. Validate rate is within bounds
  const compensation = await getJobCompensation(metadata.compensationId);
  validateRateWithinBounds(proposedRate, compensation.minRate, compensation.maxRate);

  // 3. Determine who is making the offer
  const madeBy = determineOfferMaker(ctx.user, matchId);

  // 4. Create new offer in metadata
  const newOffer: RateOffer = {
    id: createId(),
    proposedRate,
    madeBy,
    madeAt: new Date().toISOString(),
    message,
    expiresAt: expiresAt?.toISOString(),
    status: "PENDING",
  };

  // 5. Update metadata with new offer
  const updatedMetadata = {
    ...metadata,
    currentOfferRate: proposedRate,
    lastOfferBy: madeBy,
    lastOfferAt: newOffer.madeAt,
    offerExpiresAt: expiresAt?.toISOString(),
    offerHistory: [...metadata.offerHistory, newOffer],
    currentRound: metadata.currentRound + 1,
  };

  // 6. Update step metadata and compensation
  await Promise.all([
    updateStepMetadata(step.id, updatedMetadata),
    updateJobCompensation(compensation.id, {
      currentOfferRate: proposedRate,
      lastOfferBy: madeBy,
      offerExpiresAt: expiresAt,
      negotiationCount: compensation.negotiationCount + 1,
    }),
  ]);

  // 7. Log action and send notifications
  await logAction("SUBMIT_RATE_OFFER", {
    matchId,
    stepId: step.id,
    proposedRate,
    madeBy
  });

  // 8. Schedule expiration job if needed
  if (expiresAt) {
    await scheduleOfferExpiration(newOffer.id, expiresAt);
  }

  return { offer: newOffer, metadata: updatedMetadata };
}
```

### **Step 3: Respond to Offers**

```typescript
// API: matches.rateNegotiation.respondToOffer
async function respondToRateOffer({
  matchId,
  offerId,
  response,
  counterOfferRate?,
  declineReason?,
  message?
}: {
  matchId: string;
  offerId: string;
  response: "ACCEPT" | "DECLINE" | "COUNTER";
  counterOfferRate?: number;
  declineReason?: string;
  message?: string;
}) {
  // 1. Get step and find offer
  const step = await getRateNegotiationStep(matchId);
  const metadata = step.metadata as RateNegotiationMetadata;
  const offer = metadata.offerHistory.find(o => o.id === offerId);

  if (!offer || offer.status !== "PENDING") {
    throw new Error("Offer not found or not pending");
  }

  // 2. Update offer status
  offer.status = response === "ACCEPT" ? "ACCEPTED" : "DECLINED";
  offer.respondedAt = new Date().toISOString();
  if (declineReason) offer.declineReason = declineReason;

  let result: any = { offer };

  // 3. Handle response type
  if (response === "ACCEPT") {
    // Finalize negotiation
    await finalizeRateNegotiation(matchId, offer.proposedRate);
    result.finalized = true;

  } else if (response === "COUNTER" && counterOfferRate) {
    // Submit counter offer
    const counterOffer = await submitRateOffer({
      matchId,
      proposedRate: counterOfferRate,
      message,
    });
    result.counterOffer = counterOffer;
  }

  // 4. Update metadata
  const updatedMetadata = {
    ...metadata,
    offerHistory: metadata.offerHistory.map(o =>
      o.id === offerId ? offer : o
    ),
  };

  await updateStepMetadata(step.id, updatedMetadata);

  // 5. Log action
  const actionType = response === "ACCEPT" ? "ACCEPT_RATE_OFFER" :
                     response === "COUNTER" ? "COUNTER_RATE_OFFER" :
                     "DECLINE_RATE_OFFER";

  await logAction(actionType, {
    matchId,
    stepId: step.id,
    offerId,
    proposedRate: offer.proposedRate
  });

  return result;
}
```

### **Step 4: Finalize Negotiation**

```typescript
// API: matches.rateNegotiation.finalize
async function finalizeRateNegotiation(matchId: string, agreedRate: number) {
  // 1. Get step and compensation
  const step = await getRateNegotiationStep(matchId);
  const metadata = step.metadata as RateNegotiationMetadata;
  const compensation = await getJobCompensation(metadata.compensationId);

  // 2. Update compensation with final rate
  await updateJobCompensation(compensation.id, {
    finalAgreedRate: agreedRate,
    negotiationStatus: "FINALIZED",
  });

  // 3. Update job post with negotiated rate
  await updateJobPost(compensation.jobId, {
    finalNegotiatedRate: agreedRate,
  });

  // 4. Complete the rate negotiation step
  await completeMatchStep(step.id, {
    isSuccessful: true,
    notes: `Rate finalized at $${agreedRate}`,
  });

  // 5. Update match status to move to next step
  await updateMatchStatus(matchId, "FINALIZING");

  // 6. Log action and notify
  await logAction("FINALIZE_RATE_NEGOTIATION", {
    matchId,
    stepId: step.id,
    finalRate: agreedRate,
  });

  // 7. Trigger next workflow step (if any)
  await progressMatchWorkflow(matchId);

  return { finalRate: agreedRate, completed: true };
}
```

---

## Integration with Existing Systems

### **Messages API Integration**

**Current State:** ✅ **Already Supports Matches**

- `Thread` model has `matchId` field
- Messages API already handles match-based conversations
- No changes needed

**Usage Example:**

```typescript
// Get or create thread for match
const thread = await ctx.prisma.thread.upsert({
  where: { matchId },
  create: {
    matchId,
    users: { connect: [{ id: providerId }, { id: orgUserId }] },
  },
  update: {},
});

// Send rate negotiation message
await ctx.prisma.message.create({
  data: {
    content: `I've submitted a rate offer of $${proposedRate}/hour`,
    threadId: thread.id,
    authorId: ctx.session.userId,
  },
});
```

### **Contracts API Integration**

**Current State:** ✅ **Already Has Contract System**

- `Contract` model already exists
- Need to add match-based contract creation

**Enhancement Needed:**

```typescript
// Add to contracts router
createFromMatch: protectedProcedure
  .input(z.object({ matchId: z.string() }))
  .mutation(async ({ ctx, input }) => {
    // Get finalized match
    const match = await ctx.prisma.match.findUnique({
      where: { id: input.matchId },
      include: {
        compensation: true,
        job: true,
        provider: true,
        organization: true,
      },
    });

    // Create contract with agreed rate
    const contract = await ctx.prisma.contract.create({
      data: {
        matchId: input.matchId,
        providerId: match.providerId,
        organizationId: match.organizationId,
        rate: match.compensation.finalAgreedRate,
        type: "INDEPENDENT_CONTRACTOR",
        status: "DRAFT",
      },
    });

    return contract;
  });
```

### **Actions API Integration**

**Current State:** ✅ **Already Supports Extensible Actions**

- `Action` model has flexible metadata field
- Notification system already in place
- Need to add new action types

**Enhancement:**

```typescript
// Add to action handlers
export const matchActionLogic: ActionLogicFunction = async (
  payload,
  collectors,
) => {
  switch (payload.type) {
    case ActionType.SUBMIT_RATE_OFFER:
      // Notify the other party
      if (payload.metadata?.madeBy === "PROVIDER") {
        collectors.organizationNotifications.push({
          organizationId: payload.organizationId!,
          subject: "New Rate Offer",
          message: `Provider submitted rate offer: $${payload.metadata.proposedRate}`,
        });
      } else {
        collectors.providerNotifications.push({
          providerId: payload.providerId!,
          subject: "New Rate Offer",
          message: `Organization submitted rate offer: $${payload.metadata.proposedRate}`,
        });
      }
      break;

    case ActionType.FINALIZE_RATE_NEGOTIATION:
      // Notify both parties
      collectors.organizationNotifications.push({
        organizationId: payload.organizationId!,
        subject: "Rate Negotiation Complete",
        message: `Rate finalized at $${payload.metadata.finalRate}`,
      });
      collectors.providerNotifications.push({
        providerId: payload.providerId!,
        subject: "Rate Negotiation Complete",
        message: `Rate finalized at $${payload.metadata.finalRate}`,
      });
      break;
  }
};
```

---

## Implementation Phases

### **Phase 1: Core Match & Step Infrastructure**

- ✅ Database schema (completed)
- 🔄 Basic match CRUD operations
- 🔄 Match step management
- 🔄 Basic step metadata handling

### **Phase 2: Rate Negotiation Implementation**

- 🔄 Rate negotiation step initialization
- 🔄 Offer submission and response handling
- 🔄 Application-level offer tracking in metadata
- 🔄 Integration with existing JobCompensation

### **Phase 3: System Integration**

- 🔄 Actions and notifications
- 🔄 Contract creation from finalized matches
- 🔄 Message threading (already supported)
- 🔄 Expiration and automation

### **Phase 4: Advanced Features**

- 🔄 Rate strategy automation
- 🔄 Analytics and reporting
- 🔄 Bulk operations
- 🔄 Admin overrides

---

## File Structure

```
packages/api-medical/src/router/
├── matches/
│   ├── index.ts              # Main router export
│   ├── matches.ts            # Core match operations
│   ├── steps.ts              # Step management
│   └── rateNegotiation.ts    # Rate negotiation specialization
├── messages/                 # ✅ Existing - no changes needed
├── contracts/                # ✅ Existing - minor enhancement
├── actions/                  # ✅ Existing - add new action types
└── jobs/                     # ✅ Existing - reference only
```

This architecture leverages the existing message, contract, and action systems while implementing rate negotiation as a specialized match step workflow using JSON metadata for application-level state management.
