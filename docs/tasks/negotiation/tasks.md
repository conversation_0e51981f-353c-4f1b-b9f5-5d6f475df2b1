# Match-Based Step Workflow System Implementation Tasks

## 🎯 Overview

Implementation of a **step-based match workflow system** with **compensation negotiation** as the primary focus. The system leverages existing Schedule and Contract tables while introducing a flexible step architecture for compensation negotiations within predefined ranges.

**Estimated Timeline:** 8-10 weeks total
**Total Tasks:** 28 granular tasks across 4 phases  
**Architecture:** Step-based workflow system integrating with existing Schedule/Contract infrastructure

### 🏛️ Production-Safe Architecture

The Match-based step workflow system introduces a container model that **enhances** rather than replaces the existing Application/Offer workflow. This system focuses on compensation negotiation while leveraging existing Schedule and Contract models. Zero breaking changes while adding focused compensation negotiation capabilities.

```mermaid
---
title: Match-Based Step Workflow Architecture (Production Safe)
---
erDiagram
    %% Existing Production System (Unchanged)
    JobPost ||--o{ Application : "receives"
    JobPost ||--o{ Offer : "makes"
    Organization ||--o{ Application : "receives"
    Organization ||--o{ Offer : "makes"
    Provider ||--o{ Application : "submits"
    Provider ||--o{ Offer : "receives"

    Application ||--o| JobPosition : "creates"
    Offer ||--o| JobPosition : "creates"
    Application ||--o| Contract : "generates"
    Offer ||--o| Contract : "generates"

    %% New Match System (Non-Breaking Addition)
    JobPost ||--o{ Match : "enables"
    Organization ||--o{ Match : "participates"
    Provider ||--o{ Match : "participates"

    %% Match Integration (Optional Links)
    Match ||--o| Application : "enhances"
    Match ||--o| Offer : "enhances"
    Match ||--o| JobPosition : "results_in"
    Match ||--o| Contract : "generates"
    Match ||--o| Thread : "communicates"

            %% Step Workflow Within Match
    Match ||--o{ MatchStep : "contains"
    MatchStep ||--o{ StepOffer : "includes"
    MatchStep ||--o| CompensationDetails : "details"

    StepOffer ||--o| CompensationOfferDetails : "specifies"

    %% Integration with Existing Tables
    Match ||--o| Schedule : "uses existing"
    Match ||--o| Contract : "uses existing"

    %% Audit Trail Integration
    Action ||--o{ Match : "tracks"
    Action ||--o{ MatchStep : "tracks"
    Action ||--o{ StepOffer : "tracks"

    Match {
        string id PK
        enum status "PENDING|INTERVIEWING|NEGOTIATING|MATCHED"
        enum initiatedBy "PROVIDER|ORGANIZATION|MUTUAL"
        datetime interviewAt
        string interviewStatus
        string applicationId FK
        string offerId FK
        string positionId FK
        string contractId FK
    }

    MatchStep {
        string id PK
        string matchId FK
        enum type "COMPENSATION_NEGOTIATION"
        enum status "PENDING|IN_PROGRESS|COMPLETED|FAILED"
        int stepOrder
        boolean isRequired
        boolean isSuccessful
        json outcome
    }

    CompensationDetails {
        string id PK
        string matchStepId FK
        float minRate
        float maxRate
        float finalAgreedRate
        enum paymentType "HOURLY|SALARY|PER_DIEM"
        float nightRateMultiplier
        float overtimeRateMultiplier
        float callbackRate
    }
```

### 🎯 Production-Safe Benefits

- **🛡️ Zero Breaking Changes**: Existing Application/Offer workflow continues unchanged
- **🔄 Gradual Migration**: Can implement Match system alongside existing flow
- **💰 Focused Compensation**: Dedicated compensation negotiation with existing Schedule/Contract integration
- **🎯 Match Container**: Single source of truth for provider-job relationships
- **🚀 Future Ready**: Step architecture supports additional negotiation types later
- **🔧 Maintainability**: Clear separation leveraging existing Schedule/Contract infrastructure
- **📊 Unified Reporting**: Analytics across traditional and match-based compensation
- **🔗 Seamless Integration**: Works with existing Thread, Schedule, Contract, and Action systems

### 🔄 Migration Strategy

1. **Phase 1**: Deploy Match system alongside existing Application/Offer (no impact)
2. **Phase 2**: New jobs can optionally enable Match-based workflow
3. **Phase 3**: Gradually migrate high-value jobs to Match system
4. **Phase 4**: Eventually deprecate direct Application/Offer for complex roles

This approach allows you to **test and validate** the Match system in production without any risk to existing operations.

---

## 🏗️ Phase 1: Database Foundation (8 tasks)

_Estimated: 2-3 weeks_

### 1.1 Create Match database model (Production Safe)

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add Match model as container for provider-job relationships with multiple negotiations and interview workflow

**Implementation Details:**

```prisma
enum MatchStatus {
  PENDING       // Initial match created
  INTERVIEWING  // Interview process active
  NEGOTIATING   // In negotiation phase
  FINALIZING    // Terms agreed, finalizing details
  MATCHED       // Successfully matched
  WITHDRAWN     // Provider withdrew
  DECLINED      // Organization declined
  EXPIRED       // Match expired
  CANCELLED     // Cancelled by either party
  @@schema("public")
}

enum MatchInitiatedBy {
  PROVIDER      // Provider applied first
  ORGANIZATION  // Organization reached out first
  MUTUAL        // Mutual interest
  REFERRAL      // Third party referral
  @@schema("public")
}

model Match {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  status       MatchStatus      @default(PENDING)
  initiatedBy  MatchInitiatedBy @default(PROVIDER)

  // Workflow progression
  interviewAt     DateTime?
  interviewStatus String?        // "SCHEDULED", "COMPLETED", "CANCELLED"
  finalizedAt     DateTime?
  matchedAt       DateTime?

  // Match metadata
  title       String?          // e.g., "RN Position - Emergency Department"
  description String?          // Match context/notes
  priority    String?          // "LOW", "MEDIUM", "HIGH", "URGENT"

  // Core relationships - maintaining existing patterns
  job            JobPost      @relation(fields: [jobId], references: [id])
  jobId          String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  provider       Provider     @relation(fields: [providerId], references: [id])
  providerId     String

  // Integration with existing system (non-breaking)
  application    Application? @relation(fields: [applicationId], references: [id])
  applicationId  String?      @unique
  offer          Offer?       @relation(fields: [offerId], references: [id])
  offerId        String?      @unique

  // Match outcomes
  position       JobPosition? @relation(fields: [positionId], references: [id])
  positionId     String?      @unique
  contract       Contract?    @relation(fields: [contractId], references: [id])
  contractId     String?      @unique

  // New step-based workflow system
  steps MatchStep[]

  // Communication and actions
  thread         Thread?      @relation(fields: [threadId], references: [id])
  threadId       String?      @unique
  actions        Action[]

  @@unique([jobId, providerId]) // One match per job-provider pair
  @@index([status, organizationId, providerId, jobId, initiatedBy], name: "match_idx")
  @@schema("public")
}
```

### 1.2 Create MatchStep database model

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add MatchStep model supporting multiple step types within a Match workflow

**Implementation Details:**

```prisma
enum StepType {
  COMPENSATION_NEGOTIATION
  // Future: INTERVIEW, BENEFITS_NEGOTIATION, etc.
  @@schema("public")
}

enum StepStatus {
  PENDING      // Waiting to start
  IN_PROGRESS  // Currently active
  COMPLETED    // Successfully finished
  SKIPPED      // Bypassed (optional step)
  FAILED       // Failed to complete
  CANCELLED    // Cancelled by either party
  ON_HOLD      // Temporarily paused
  @@schema("public")
}

model MatchStep {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  type             StepType   @default(COMPENSATION_NEGOTIATION)
  status           StepStatus @default(PENDING)

  // Step metadata
  title            String?           // e.g., "Compensation Discussion", "Rate Negotiation"
  description      String?           // Context for this step
  stepOrder        Int               // Order in workflow (1, 2, 3...)
  isRequired       Boolean @default(true)  // Can this step be skipped?

  // Timeline
  startedAt        DateTime?
  completedAt      DateTime?
  dueAt            DateTime?

  // Results
  isSuccessful     Boolean?          // null = pending, true/false = result
  outcome          Json?             // Step outcome/results in JSON
  notes            String?           // Any notes from this step

  // Relationships
  match            Match             @relation(fields: [matchId], references: [id], onDelete: Cascade)
  matchId          String

  // Step-specific details - focused on compensation
  compensationDetails CompensationDetails?
  // Future: interviewDetails, benefitsDetails, etc.

  offers           StepOffer[]
  actions          Action[]

  @@unique([matchId, type]) // One step per type per match
  @@index([status, matchId, stepOrder], name: "match_step_workflow_idx")
  @@index([type, status], name: "match_step_type_status_idx")
  @@schema("public")
}
```

### 1.3 Create CompensationDetails model

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add compensation-specific details with comprehensive rate and pay structure

**Implementation Details:**

```prisma
model CompensationDetails {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Compensation boundaries (from JobPost)
  minRate          Float
  maxRate          Float

  // Current negotiation state
  currentOfferRate Float?
  finalAgreedRate  Float?

  // Payment structure context
  paymentType      PayType @default(HOURLY)

  // Complex rate structures for medical professionals
  baseRate         Float?
  nightRateMultiplier    Float?   @default(1.25)
  overtimeRateMultiplier Float?   @default(1.5)
  holidayRateMultiplier  Float?   @default(2.0)
  weekendRateMultiplier  Float?   @default(1.1)
  onCallRateMultiplier   Float?   @default(0.5)

  // Special medical rates
  callbackRate     Float?           // Rate for emergency callbacks
  orientationRate  Float?           // Rate during orientation period
  supervisionRate  Float?           // Rate when supervising others

  // Compensation calculation context
  expectedHoursPerWeek   Int?
  expectedShiftsPerMonth Int?
  totalMonthlyValue      Float?     // Calculated total compensation

  matchStep   MatchStep @relation(fields: [matchStepId], references: [id], onDelete: Cascade)
  matchStepId String    @unique

  @@index([matchStepId, minRate, maxRate], name: "compensation_details_idx")
  @@schema("public")
}
```

### 1.4 Create StepOffer model

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add offer model for back-and-forth negotiations within match steps

**Implementation Details:**

```prisma
enum OfferStatus {
  PENDING     // Awaiting response
  ACCEPTED    // Offer accepted
  DECLINED    // Offer declined
  EXPIRED     // Offer timeout
  WITHDRAWN   // Offer withdrawn by sender
  SUPERSEDED  // Replaced by new offer
  @@schema("public")
}

enum OfferType {
  INITIAL     // First offer in negotiation
  COUNTER     // Counter-offer response
  FINAL       // Final offer (take it or leave it)
  CLARIFICATION // Request for clarification
  @@schema("public")
}

enum OfferMadeBy {
  PROVIDER
  ORGANIZATION
  @@schema("public")
}

model StepOffer {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  type          OfferType   @default(INITIAL)
  status        OfferStatus @default(PENDING)
  madeBy        OfferMadeBy

  // Offer content
  message       String?     // Human-readable offer description
  terms         Json        // Structured offer terms

  // Response handling
  declineReason String?
  responseNotes String?

  // Timing
  expiresAt     DateTime?
  respondedAt   DateTime?

  // Offer details - focused on compensation
  compensationOffer CompensationOfferDetails?
  // Future: benefitsOffer, contractOffer, etc.

  matchStep   MatchStep @relation(fields: [matchStepId], references: [id], onDelete: Cascade)
  matchStepId String

  actions Action[]

  @@index([status, matchStepId, madeBy, type], name: "step_offer_idx")
  @@schema("public")
}
```

### 1.5 Create CompensationOfferDetails model

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add detailed compensation offer structure

**Implementation Details:**

```prisma
model CompensationOfferDetails {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Primary compensation offer
  proposedRate         Float
  paymentType          PayType @default(HOURLY)

  // Detailed compensation breakdown for complex medical compensation
  baseRate             Float?
  nightRateMultiplier  Float?
  overtimeRateMultiplier Float?
  holidayRateMultiplier  Float?
  weekendRateMultiplier  Float?
  onCallRateMultiplier   Float?

  // Special rates
  callbackRate         Float?
  orientationRate      Float?
  supervisionRate      Float?

  // Value calculations
  proposedHoursPerWeek Int?
  totalProposedMonthlyValue Float?
  effectiveHourlyRate       Float?

  // Additional compensation
  signOnBonus          Float?
  completionBonus      Float?
  performanceIncentives Json?  // Structured performance bonuses

  // Benefits context (if applicable)
  includesBenefits     Boolean @default(false)
  benefitsValue        Float?
  benefitsDescription  String?

  stepOffer StepOffer @relation(fields: [stepOfferId], references: [id], onDelete: Cascade)
  stepOfferId String  @unique

  @@index([stepOfferId, proposedRate], name: "compensation_offer_details_idx")
  @@schema("public")
}
```

### 1.6 Update existing models for Match integration (Non-Breaking)

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add optional Match relationships to existing models without breaking changes

**Implementation Details:**

```prisma
// Add to existing Application model
model Application {
  // ... all existing fields unchanged ...

  // New optional relationship to Match (non-breaking)
  match   Match? // Reverse relation

  // ... all existing relations unchanged ...
}

// Add to existing Offer model
model Offer {
  // ... all existing fields unchanged ...

  // New optional relationship to Match (non-breaking)
  match   Match? // Reverse relation

  // ... all existing relations unchanged ...
}

// Add to existing JobPost model
model JobPost {
  // ... all existing fields unchanged ...

  // New compensation negotiation settings (non-breaking additions)
  allowCompensationNegotiation Boolean @default(false)
  minNegotiableRate           Float?
  maxNegotiableRate           Float?

  // Integration with existing Schedule table (no changes needed)
  // Uses existing Schedule model for schedule management
  scheduleFlexibilityLevel String? // "LOW", "MEDIUM", "HIGH"

  // New relations (non-breaking)
  matches Match[]

  // ... all existing relations unchanged ...
}

// Add to existing Thread model
model Thread {
  // ... all existing fields unchanged ...

  // New optional relationship to Match (non-breaking)
  match   Match?

  // ... all existing relations unchanged ...
}
```

### 1.7 Update Action model for Match and Step tracking

**File:** `packages/db-medical/prisma/schema.prisma`
**Description:** Add Match and MatchStep relationships to Action model

**Implementation Details:**

```prisma
model Action {
  // ... all existing fields unchanged ...

  // New negotiation and match relations
  match        Match?     @relation(fields: [matchId], references: [id])
  matchId      String?
  matchStep    MatchStep? @relation(fields: [matchStepId], references: [id])
  matchStepId  String?
  stepOffer    StepOffer? @relation(fields: [stepOfferId], references: [id])
  stepOfferId  String?

  // ... all existing relations unchanged ...

  // Update index to include new fields
  @@index([actorId, organizationId, jobId, shiftId, locationId, providerId, offerId, applicationId, contractId, qualificationId, documentId, buildingId, departmentId, reviewId, invoiceId, jobExperienceId, scheduleId, contactId, incidentId, providerVerificationId, organizationSettingId, matchId, matchStepId, stepOfferId], name: "action_idx")
}
```

### 1.8 Create and run database migration (Production Safe)

**Command:** `npx prisma migrate dev --name add-match-compensation-system`
**Description:** Generate and execute production-safe migration for the Match-based compensation negotiation system

**Migration Strategy:**

1. **Phase 1**: Add new tables (Match, MatchStep, CompensationDetails, etc.) - Zero impact
2. **Phase 2**: Add optional foreign keys to existing tables - Zero impact
3. **Phase 3**: Create indexes for performance - Minimal impact
4. **Phase 4**: Verify all constraints and relationships - Zero impact

**Safety Checks:**

- All new fields are optional or have defaults
- No existing data is modified
- All existing queries continue to work
- Rollback procedure tested and documented

**Production Deployment Checklist:**

- [ ] Run migration on staging environment first
- [ ] Verify existing Application/Offer queries work unchanged
- [ ] Test rollback procedure on staging
- [ ] Monitor database performance during migration
- [ ] Deploy during low-traffic window
- [ ] Verify no impact on existing user workflows

---

## ⚙️ Phase 2: Core API Implementation (15 tasks)

_Estimated: 3-4 weeks_

### 2.1 Add rate negotiation action types

**File:** `packages/api-medical/src/constants/actions.ts`
**Description:** Extend ActionType and ResourceType enums with all rate negotiation action types

**Implementation Details:**

```typescript
// Add to existing ActionType enum
export enum ActionType {
  // ... existing actions ...

  // Rate negotiation actions
  START_RATE_NEGOTIATION = "START_RATE_NEGOTIATION",
  SUBMIT_RATE_OFFER = "SUBMIT_RATE_OFFER",
  ACCEPT_RATE_OFFER = "ACCEPT_RATE_OFFER",
  DECLINE_RATE_OFFER = "DECLINE_RATE_OFFER",
  WITHDRAW_RATE_OFFER = "WITHDRAW_RATE_OFFER",
  EXPIRE_RATE_OFFER = "EXPIRE_RATE_OFFER",
  FINALIZE_RATE_NEGOTIATION = "FINALIZE_RATE_NEGOTIATION",
  CANCEL_RATE_NEGOTIATION = "CANCEL_RATE_NEGOTIATION",
}

// Add to existing ResourceType enum
export enum ResourceType {
  // ... existing resources ...
  RATE_NEGOTIATION = "RATE_NEGOTIATION",
  RATE_OFFER = "RATE_OFFER",
}
```

### 2.2 Create negotiations router directory structure

**Directory:** `packages/api-medical/src/router/negotiations/`
**Description:** Create directory with index.ts, negotiations.ts, offers.ts, and timeline.ts files

**File Structure:**

```
negotiations/
├── index.ts          # Router composition and exports
├── negotiations.ts   # Core negotiation operations
├── offers.ts        # Rate offer management
└── timeline.ts      # History and timeline features
```

### 2.3 Implement core negotiations router

**File:** `packages/api-medical/src/router/negotiations/negotiations.ts`
**Description:** Create negotiations.ts with start, get, cancel, and getByJob/getByProvider/getByOrganization endpoints

**Key Endpoints:**

- `start` - Start new negotiation with validation
- `get` - Get negotiation details with permissions
- `cancel` - Cancel active negotiation
- `getByJob` - Get all negotiations for a job
- `getByProvider` - Get provider's negotiations
- `getByOrganization` - Get organization's negotiations

**Example Implementation:**

```typescript
export const negotiationsRouter = createTRPCRouter({
  start: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        initialOfferRate: z.number().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Validate permissions and job settings
      // 2. Check for existing negotiation
      // 3. Create negotiation record
      // 4. Log action and send notifications
    }),

  get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // Get negotiation with permission validation
    }),

  getByJob: protectedProcedure
    .input(z.object({ jobId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Get all negotiations for a job
    }),
});
```

### 2.4 Implement rate offers router

**File:** `packages/api-medical/src/router/negotiations/offers.ts`
**Description:** Create offers.ts with submit, respond (accept/decline), withdraw, and getActive endpoints

**Key Endpoints:**

- `submit` - Submit new rate offer with validation
- `respond` - Accept/decline offers with state updates
- `withdraw` - Withdraw pending offers
- `getActive` - Get all active offers for user

**Example Implementation:**

```typescript
export const rateOffersRouter = createTRPCRouter({
  submit: protectedProcedure
    .input(
      z.object({
        negotiationId: z.string(),
        proposedRate: z.number(),
        message: z.string().optional(),
        expiresAt: z.date().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Validate negotiation and permissions
      // 2. Check rate boundaries
      // 3. Create offer record
      // 4. Update negotiation state
      // 5. Log action and notify
    }),

  respond: protectedProcedure
    .input(
      z.object({
        offerId: z.string(),
        action: z.enum(["accept", "decline"]),
        declineReason: z.string().optional(),
        counterOfferRate: z.number().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Handle accept/decline/counter logic
    }),
});
```

### 2.5 Implement timeline router

**File:** `packages/api-medical/src/router/negotiations/timeline.ts`
**Description:** Create timeline.ts with getTimeline, getOfferHistory, and getMergedTimeline endpoints

**Key Features:**

- Merge actions and offers chronologically
- Filter by event type and date range
- Include actor information and metadata
- Support pagination for large timelines

**Example Implementation:**

```typescript
export const negotiationTimelineRouter = createTRPCRouter({
  getTimeline: protectedProcedure
    .input(
      z.object({
        negotiationId: z.string(),
        includeMessages: z.boolean().default(false),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Get all actions and offers
      // Merge and sort chronologically
      // Return unified timeline
    }),
});
```

### 2.6 Create permission validation helpers

**File:** `packages/api-medical/src/lib/permissions/negotiations.ts`
**Description:** Implement validateNegotiationPermissions and related authorization helper functions

**Key Functions:**

- `validateNegotiationPermissions` - Check read/write access
- `canStartNegotiation` - Validate negotiation start permissions
- `canSubmitOffer` - Validate offer submission permissions
- `isNegotiationParticipant` - Check if user is part of negotiation

**Example Implementation:**

```typescript
export async function validateNegotiationPermissions(
  ctx: Context,
  negotiationId: string,
  action: "read" | "write",
) {
  const negotiation = await ctx.prisma.rateNegotiation.findUnique({
    where: { id: negotiationId },
    select: { providerId: true, organizationId: true },
  });

  const isProvider = ctx.user.providerId === negotiation?.providerId;
  const isOrgMember = ctx.user.organizationId === negotiation?.organizationId;

  if (!isProvider && !isOrgMember && ctx.user.role !== "ADMIN") {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Not authorized to access this negotiation",
    });
  }

  return { isProvider, isOrgMember, negotiation };
}
```

### 2.7 Add negotiations router to main API

**File:** `packages/api-medical/src/router/index.ts`
**Description:** Register rateNegotiationsRouter in main API router

**Implementation:**

```typescript
import { rateNegotiationsRouter } from "./negotiations";

export const appRouter = createTRPCRouter({
  // ... existing routers ...
  rateNegotiations: rateNegotiationsRouter,
});
```

### 2.8 Implement business rule validation

**File:** `packages/api-medical/src/lib/validation/negotiations.ts`
**Description:** Create rate validation, negotiation state checking, and workflow rule enforcement functions

**Key Validations:**

- Rate within job boundaries
- Negotiation state transitions
- One negotiation per job-provider pair
- Offer expiration handling

**Example Functions:**

```typescript
export function validateRateWithinBounds(
  rate: number,
  minRate: number,
  maxRate: number,
) {
  if (rate < minRate || rate > maxRate) {
    throw new ValidationError(
      `Rate must be between $${minRate} and $${maxRate}`,
    );
  }
}

export function validateNegotiationState(
  status: RateNegotiationStatus,
  allowedActions: string[],
) {
  // Validate state transitions
}
```

### 2.9 Define Zod validation schemas

**File:** `packages/api-medical/src/schemas/negotiations.ts`
**Description:** Create comprehensive Zod schemas for all rate negotiation input validation in API endpoints

**Schemas:**

```typescript
export const zStartNegotiationInput = z.object({
  jobId: z.string().cuid(),
  initialOfferRate: z.number().positive().optional(),
  message: z.string().max(1000).optional(),
});

export const zSubmitOfferInput = z.object({
  negotiationId: z.string().cuid(),
  proposedRate: z.number().positive(),
  message: z.string().max(1000).optional(),
  expiresAt: z.date().min(new Date()).optional(),
});

export const zRespondToOfferInput = z.object({
  offerId: z.string().cuid(),
  action: z.enum(["accept", "decline"]),
  declineReason: z.string().max(500).optional(),
  counterOfferRate: z.number().positive().optional(),
});
```

### 2.10 Implement rate boundary validation logic

**File:** `packages/api-medical/src/lib/validation/rates.ts`
**Description:** Create functions to validate proposed rates against job min/max ranges with proper error messages

**Functions:**

```typescript
export function validateRateWithinBounds(
  rate: number,
  minRate: number,
  maxRate: number,
) {
  if (rate < minRate) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: `Rate $${rate} is below minimum of $${minRate}`,
    });
  }
  if (rate > maxRate) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: `Rate $${rate} exceeds maximum of $${maxRate}`,
    });
  }
}

export function formatRateValidationError(
  rate: number,
  minRate: number,
  maxRate: number,
) {
  return `Proposed rate $${rate} must be between $${minRate} and $${maxRate}`;
}

export function calculateRateDeviation(
  rate: number,
  minRate: number,
  maxRate: number,
) {
  const midpoint = (minRate + maxRate) / 2;
  return ((rate - midpoint) / midpoint) * 100;
}
```

### 2.11 Create negotiation state machine

**File:** `packages/api-medical/src/lib/state/negotiations.ts`
**Description:** Implement state transition logic for negotiations (ACTIVE -> FINALIZED/EXPIRED/CANCELLED)

**State Transitions:**

- ACTIVE -> FINALIZED (when offer accepted)
- ACTIVE -> EXPIRED (when timeout reached)
- ACTIVE -> CANCELLED (manual cancellation)
- Validation for invalid transitions

**Implementation:**

```typescript
export enum NegotiationState {
  ACTIVE = "ACTIVE",
  FINALIZED = "FINALIZED",
  EXPIRED = "EXPIRED",
  CANCELLED = "CANCELLED",
}

export function validateStateTransition(
  currentState: NegotiationState,
  newState: NegotiationState,
): boolean {
  const validTransitions = {
    [NegotiationState.ACTIVE]: [
      NegotiationState.FINALIZED,
      NegotiationState.EXPIRED,
      NegotiationState.CANCELLED,
    ],
    [NegotiationState.FINALIZED]: [],
    [NegotiationState.EXPIRED]: [],
    [NegotiationState.CANCELLED]: [],
  };

  return validTransitions[currentState].includes(newState);
}
```

### 2.12 Implement offer response workflows

**File:** `packages/api-medical/src/lib/workflows/offers.ts`
**Description:** Create accept/decline/counter-offer logic with proper state updates and finalization handling

**Workflows:**

- Accept offer -> finalize negotiation
- Decline offer -> update status, optional counter
- Counter offer -> create new offer, update negotiation
- Automatic job rate update on finalization

**Implementation:**

```typescript
export async function acceptOffer(ctx: Context, offerId: string) {
  return ctx.prisma.$transaction(async (tx) => {
    // 1. Update offer status to ACCEPTED
    const offer = await tx.rateOffer.update({
      where: { id: offerId },
      data: { status: "ACCEPTED", respondedAt: new Date() },
      include: { negotiation: true },
    });

    // 2. Finalize negotiation
    await tx.rateNegotiation.update({
      where: { id: offer.negotiationId },
      data: {
        status: "FINALIZED",
        finalAgreedRate: offer.proposedRate,
      },
    });

    // 3. Update job with final rate
    await tx.jobPost.update({
      where: { id: offer.negotiation.jobId },
      data: { finalNegotiatedRate: offer.proposedRate },
    });

    // 4. Log action
    await performAction({
      type: ActionType.ACCEPT_RATE_OFFER,
      resourceType: ResourceType.RATE_OFFER,
      resourceId: offerId,
      // ... other action data
    });

    return offer;
  });
}
```

### 2.13 Create error handling and logging

**File:** `packages/api-medical/src/lib/errors/negotiations.ts`
**Description:** Implement comprehensive error handling, logging, and monitoring for all rate negotiation operations

**Error Types:**

```typescript
export class NegotiationNotFoundError extends TRPCError {
  constructor(negotiationId: string) {
    super({
      code: "NOT_FOUND",
      message: `Negotiation ${negotiationId} not found`,
    });
  }
}

export class InvalidRateError extends TRPCError {
  constructor(rate: number, minRate: number, maxRate: number) {
    super({
      code: "BAD_REQUEST",
      message: `Rate $${rate} must be between $${minRate} and $${maxRate}`,
    });
  }
}

export class PermissionDeniedError extends TRPCError {
  constructor(action: string) {
    super({
      code: "FORBIDDEN",
      message: `Permission denied for action: ${action}`,
    });
  }
}

export class InvalidStateTransitionError extends TRPCError {
  constructor(currentState: string, newState: string) {
    super({
      code: "BAD_REQUEST",
      message: `Invalid state transition from ${currentState} to ${newState}`,
    });
  }
}
```

### 2.14 Implement rate calculation utilities

**File:** `packages/api-medical/src/lib/utils/rates.ts`
**Description:** Create helper functions for rate calculations, comparisons, and formatting across the system

**Utilities:**

```typescript
export function formatRate(rate: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(rate);
}

export function compareRates(rate1: number, rate2: number): number {
  return rate1 - rate2;
}

export function calculateRateChange(oldRate: number, newRate: number): number {
  return ((newRate - oldRate) / oldRate) * 100;
}

export function validateRateFormat(rate: unknown): rate is number {
  return typeof rate === "number" && rate > 0 && isFinite(rate);
}
```

### 2.15 Create audit trail integration

**File:** Integration with existing action system
**Description:** Ensure all negotiation actions are properly logged in the existing audit trail system

**Integration Points:**

- Use existing `performAction` function
- Proper metadata inclusion
- Action type mapping
- Timeline integration

**Example Usage:**

```typescript
// In negotiation operations
await performAction({
  type: ActionType.START_RATE_NEGOTIATION,
  resourceType: ResourceType.RATE_NEGOTIATION,
  resourceId: negotiation.id,
  actorId: ctx.session.userId,
  organizationId: negotiation.organizationId,
  providerId: negotiation.providerId,
  metadata: {
    jobId: negotiation.jobId,
    minRate: negotiation.minRate,
    maxRate: negotiation.maxRate,
  },
});
```

---

## 🔔 Phase 3: Notification & Action System (10 tasks)

_Estimated: 2-3 weeks_

### 3.1 Create rate negotiation action handler

**File:** `packages/api-medical/src/lib/actions/handlers/rateNegotiation.ts`
**Description:** Implement notification logic for all negotiation events

**Implementation:**

```typescript
import type { ActionLogicFunction } from "../actionHandler";

import { ActionType } from "../../../constants/actions";

export const rateNegotiationActionLogic: ActionLogicFunction = async (
  payload,
  collectors,
) => {
  switch (payload.type) {
    case ActionType.START_RATE_NEGOTIATION:
      // Notify organization about new negotiation request
      collectors.organizationNotifications.push({
        organizationId: payload.organizationId!,
        subject: "New Rate Negotiation Request",
        email: `A provider has requested to negotiate rates for job ${payload.metadata?.jobTitle}`,
        text: `Rate negotiation started for ${payload.metadata?.jobTitle}`,
        topic: "jobs",
      });
      break;

    case ActionType.FINALIZE_RATE_NEGOTIATION:
      // Notify both parties of finalization
      collectors.organizationNotifications.push({
        organizationId: payload.organizationId!,
        subject: "Rate Negotiation Finalized",
        email: `Rate negotiation completed at $${payload.metadata?.finalRate}`,
        text: `Final rate agreed: $${payload.metadata?.finalRate}`,
        topic: "jobs",
      });

      collectors.providerNotifications.push({
        providerId: payload.providerId!,
        subject: "Rate Negotiation Finalized",
        email: `Rate negotiation completed at $${payload.metadata?.finalRate}`,
        text: `Final rate agreed: $${payload.metadata?.finalRate}`,
        topic: "jobs",
      });
      break;
  }
};
```

### 3.2 Create rate offer action handler

**File:** `packages/api-medical/src/lib/actions/handlers/rateOffer.ts`
**Description:** Implement notification logic for offer events

**Implementation:**

```typescript
export const rateOfferActionLogic: ActionLogicFunction = async (
  payload,
  collectors,
) => {
  switch (payload.type) {
    case ActionType.SUBMIT_RATE_OFFER:
      // Notify the other party about new offer
      if (payload.metadata?.madeBy === "PROVIDER") {
        collectors.organizationNotifications.push({
          organizationId: payload.organizationId!,
          subject: "New Rate Offer Received",
          email: `Provider has submitted a rate offer of $${payload.metadata?.proposedRate}`,
          text: `New rate offer: $${payload.metadata?.proposedRate}`,
          topic: "jobs",
        });
      } else {
        collectors.providerNotifications.push({
          providerId: payload.providerId!,
          subject: "New Rate Offer Received",
          email: `Organization has submitted a rate offer of $${payload.metadata?.proposedRate}`,
          text: `New rate offer: $${payload.metadata?.proposedRate}`,
          topic: "jobs",
        });
      }
      break;

    case ActionType.ACCEPT_RATE_OFFER:
      // Notify offer creator of acceptance
      // Implementation details...
      break;

    case ActionType.DECLINE_RATE_OFFER:
      // Notify offer creator of decline
      // Implementation details...
      break;
  }
};
```

### 3.3 Register action handlers in dispatcher

**File:** `packages/api-medical/src/lib/actions/index.ts`
**Description:** Add new action handlers to the action dispatcher system for automatic event processing

**Integration:**

```typescript
import { rateNegotiationActionLogic } from "./handlers/rateNegotiation";
import { rateOfferActionLogic } from "./handlers/rateOffer";

// Add to existing action handlers mapping
export const actionHandlers = {
  // ... existing handlers ...
  [ResourceType.RATE_NEGOTIATION]: rateNegotiationActionLogic,
  [ResourceType.RATE_OFFER]: rateOfferActionLogic,
};
```

### 3.4 Integrate with QStash notification system

**File:** `packages/api-medical/src/jobs/notifications/process-action-event.ts`
**Description:** Ensure rate negotiation actions trigger QStash jobs for asynchronous notification processing

**Verification:**

- Confirm new action types are processed by existing QStash system
- Test notification delivery pipeline
- Verify action handlers are called correctly

### 3.5 Create notification templates

**File:** `packages/api-medical/src/lib/notifications/templates/rateNegotiations.ts`
**Description:** Design email and SMS templates for all rate negotiation notification scenarios

**Templates:**

```typescript
export const rateNegotiationTemplates = {
  negotiationStarted: {
    email: {
      subject: "New Rate Negotiation Request",
      body: `
        <h2>Rate Negotiation Started</h2>
        <p>A provider has requested to negotiate rates for the following job:</p>
        <ul>
          <li>Job: {{jobTitle}}</li>
          <li>Provider: {{providerName}}</li>
          <li>Rate Range: ${{ minRate }} - ${{ maxRate }}</li>
        </ul>
        <a href="{{actionUrl}}">View Negotiation</a>
      `,
    },
    sms: "New rate negotiation request for {{jobTitle}}. View details: {{actionUrl}}",
  },

  offerSubmitted: {
    email: {
      subject: "New Rate Offer: ${{proposedRate}}",
      body: `
        <h2>New Rate Offer Received</h2>
        <p>{{senderName}} has submitted a rate offer:</p>
        <ul>
          <li>Proposed Rate: ${{ proposedRate }}</li>
          <li>Message: {{message}}</li>
          <li>Expires: {{expiresAt}}</li>
        </ul>
        <a href="{{actionUrl}}">Respond to Offer</a>
      `,
    },
    sms: "New rate offer: ${{proposedRate}} from {{senderName}}. Respond: {{actionUrl}}",
  },

  negotiationFinalized: {
    email: {
      subject: "Rate Negotiation Complete - ${{finalRate}}",
      body: `
        <h2>Negotiation Finalized</h2>
        <p>The rate negotiation has been completed:</p>
        <ul>
          <li>Final Rate: ${{ finalRate }}</li>
          <li>Job: {{jobTitle}}</li>
        </ul>
        <p>Next steps: {{nextSteps}}</p>
      `,
    },
    sms: "Rate negotiation complete at ${{finalRate}} for {{jobTitle}}",
  },
};
```

### 3.6 Implement expiration job scheduling

**File:** `packages/api-medical/src/jobs/negotiations/expiration.ts`
**Description:** Create QStash jobs for handling offer expiration and stale negotiation reminders

**Implementation:**

```typescript
import { qstash } from "@axa/lib/qstash";

export async function scheduleOfferExpiration(
  offerId: string,
  expiresAt: Date,
) {
  const delay = expiresAt.getTime() - Date.now();

  if (delay > 0) {
    await qstash.publishJSON({
      url: `${process.env.NEXTAUTH_URL}/api/jobs/expire-offer`,
      body: { offerId },
      delay: Math.floor(delay / 1000), // QStash expects seconds
    });
  }
}

export async function scheduleStaleNegotiationReminder(negotiationId: string) {
  const reminderDelay = 7 * 24 * 60 * 60; // 7 days in seconds

  await qstash.publishJSON({
    url: `${process.env.NEXTAUTH_URL}/api/jobs/stale-negotiation-reminder`,
    body: { negotiationId },
    delay: reminderDelay,
  });
}
```

### 3.7 Test notification delivery

**File:** Test files and verification procedures
**Description:** Verify all notification types are properly sent via email, SMS, and in-app channels

**Test Cases:**

- Negotiation started notifications
- Offer submitted notifications
- Offer accepted/declined notifications
- Offer expiration notifications
- Negotiation finalized notifications
- Stale negotiation reminders

**Verification Steps:**

1. Create test negotiations and offers
2. Trigger each notification scenario
3. Verify email delivery and content
4. Verify SMS delivery and content
5. Verify in-app notification creation
6. Test notification preferences handling

### 3.8 Create notification preference handling

**File:** `packages/api-medical/src/lib/notifications/preferences.ts`
**Description:** Implement user preference checking for rate negotiation notifications (email, SMS, in-app)

**Implementation:**

```typescript
export async function checkNotificationPreferences(
  personId: string,
  notificationType: "jobs" | "contracts" | "messages",
) {
  const person = await prisma.person.findUnique({
    where: { id: personId },
    select: { settings: true },
  });

  const settings = person?.settings || {};

  return {
    email: settings.emailNotifications && settings[notificationType],
    sms: settings.smsNotifications && settings[notificationType],
    inApp: settings.notifications && settings[notificationType],
  };
}

export function shouldSendNotification(
  preferences: { email: boolean; sms: boolean; inApp: boolean },
  channel: "email" | "sms" | "inApp",
): boolean {
  return preferences[channel] === true;
}
```

### 3.9 Implement notification batching and throttling

**File:** `packages/api-medical/src/lib/notifications/batching.ts`
**Description:** Create logic to batch notifications and prevent spam for active negotiations

**Implementation:**

```typescript
export class NotificationBatcher {
  private static instance: NotificationBatcher;
  private pendingNotifications = new Map<string, any[]>();
  private batchTimers = new Map<string, NodeJS.Timeout>();

  static getInstance(): NotificationBatcher {
    if (!NotificationBatcher.instance) {
      NotificationBatcher.instance = new NotificationBatcher();
    }
    return NotificationBatcher.instance;
  }

  addNotification(userId: string, notification: any) {
    if (!this.pendingNotifications.has(userId)) {
      this.pendingNotifications.set(userId, []);
    }

    this.pendingNotifications.get(userId)!.push(notification);

    // Clear existing timer and set new one
    if (this.batchTimers.has(userId)) {
      clearTimeout(this.batchTimers.get(userId)!);
    }

    // Batch notifications for 5 minutes
    const timer = setTimeout(
      () => {
        this.sendBatchedNotifications(userId);
      },
      5 * 60 * 1000,
    );

    this.batchTimers.set(userId, timer);
  }

  private async sendBatchedNotifications(userId: string) {
    const notifications = this.pendingNotifications.get(userId) || [];
    if (notifications.length === 0) return;

    // Send batched notification
    await this.sendDigestNotification(userId, notifications);

    // Clean up
    this.pendingNotifications.delete(userId);
    this.batchTimers.delete(userId);
  }
}
```

### 3.10 Create notification retry mechanism

**File:** `packages/api-medical/src/lib/notifications/retry.ts`
**Description:** Implement retry logic for failed notification deliveries with exponential backoff

**Implementation:**

```typescript
export class NotificationRetryManager {
  private static readonly MAX_RETRIES = 3;
  private static readonly BASE_DELAY = 1000; // 1 second

  static async sendWithRetry(
    sendFunction: () => Promise<void>,
    attempt: number = 1,
  ): Promise<void> {
    try {
      await sendFunction();
    } catch (error) {
      if (attempt >= this.MAX_RETRIES) {
        logger.error(
          `Notification failed after ${this.MAX_RETRIES} attempts:`,
          error,
        );
        throw error;
      }

      const delay = this.BASE_DELAY * Math.pow(2, attempt - 1);
      logger.warn(
        `Notification attempt ${attempt} failed, retrying in ${delay}ms:`,
        error,
      );

      await new Promise((resolve) => setTimeout(resolve, delay));
      return this.sendWithRetry(sendFunction, attempt + 1);
    }
  }

  static async scheduleRetry(
    notificationData: any,
    attempt: number,
  ): Promise<void> {
    const delay = this.BASE_DELAY * Math.pow(2, attempt - 1);

    await qstash.publishJSON({
      url: `${process.env.NEXTAUTH_URL}/api/jobs/retry-notification`,
      body: { ...notificationData, attempt: attempt + 1 },
      delay: Math.floor(delay / 1000),
    });
  }
}
```

---

## 🚀 Phase 4: Advanced Features & Integration (12 tasks)

_Estimated: 2-3 weeks_

### 4.1 Extend job posting system integration

**File:** `packages/api-medical/src/router/jobs/jobs.ts`
**Description:** Update job queries and selections to include rate negotiation data

**Implementation:**

```typescript
// Extend existing job selection
const jobWithNegotiationData = {
  ...existingJobSelection,
  allowRateNegotiation: true,
  minNegotiableRate: true,
  maxNegotiableRate: true,
  finalNegotiatedRate: true,
  rateNegotiations: {
    include: {
      provider: { include: { person: true } },
      offers: { orderBy: { createdAt: 'desc' }, take: 1 },
    },
  },
};

// Add to job mutations
updateJob: protectedProcedure
  .input(z.object({
    id: z.string(),
    allowRateNegotiation: z.boolean().optional(),
    minNegotiableRate: z.number().optional(),
    maxNegotiableRate: z.number().optional(),
  }))
  .mutation(async ({ ctx, input }) => {
    // Update job with negotiation settings
  }),
```

### 4.2 Integrate with messaging system

**File:** `packages/api-medical/src/router/messages/messages.ts`
**Description:** Add 'rate_negotiation' to message resource types

**Implementation:**

```typescript
// Extend existing message resource enum
export const zMessageResourceEnum = z.enum([
  "shift",
  "application",
  "offer",
  "job",
  "position",
  "incident",
  "contract",
  "rate_negotiation", // Add this
]);

// Add rate negotiation message handling
case "rate_negotiation":
  // Handle rate negotiation messages
  resourceData = await ctx.prisma.rateNegotiation.findUnique({
    where: { id: input.resourceId },
    include: { job: true, provider: true, organization: true },
  });
  break;
```

### 4.3 Implement admin dashboard features

**File:** `packages/api-medical/src/router/admin/negotiations.ts`
**Description:** Create admin endpoints for viewing all negotiations, system-wide statistics, and configuration management

**Admin Endpoints:**

```typescript
export const adminNegotiationsRouter = createTRPCRouter({
  getAllNegotiations: adminProcedure
    .input(
      z.object({
        status: z
          .enum(["ACTIVE", "FINALIZED", "EXPIRED", "CANCELLED"])
          .optional(),
        organizationId: z.string().optional(),
        dateRange: z
          .object({
            start: z.date(),
            end: z.date(),
          })
          .optional(),
        limit: z.number().default(50),
        offset: z.number().default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Return paginated negotiations with filters
    }),

  getNegotiationStats: adminProcedure.query(async ({ ctx }) => {
    // Return system-wide negotiation statistics
    return {
      totalNegotiations: await ctx.prisma.rateNegotiation.count(),
      activeNegotiations: await ctx.prisma.rateNegotiation.count({
        where: { status: "ACTIVE" },
      }),
      averageResolutionTime: 0, // Calculate from data
      successRate: 0, // Calculate completion rate
    };
  }),

  updateSystemConfig: adminProcedure
    .input(
      z.object({
        defaultOfferExpiration: z.number().optional(),
        maxNegotiationDuration: z.number().optional(),
        allowedRateDeviation: z.number().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Update system-wide configuration
    }),
});
```

### 4.4 Build comprehensive timeline system

**File:** `packages/api-medical/src/router/negotiations/timeline.ts`
**Description:** Implement advanced timeline features with filtering, search, and merged event display

**Enhanced Timeline Features:**

```typescript
export const negotiationTimelineRouter = createTRPCRouter({
  getTimeline: protectedProcedure
    .input(
      z.object({
        negotiationId: z.string(),
        eventTypes: z.array(z.enum(["action", "offer", "message"])).optional(),
        dateRange: z
          .object({
            start: z.date(),
            end: z.date(),
          })
          .optional(),
        search: z.string().optional(),
        limit: z.number().default(50),
        offset: z.number().default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Advanced timeline with filtering and search
      const events = [];

      // Get actions
      if (!input.eventTypes || input.eventTypes.includes("action")) {
        const actions = await ctx.prisma.action.findMany({
          where: {
            OR: [
              { rateNegotiationId: input.negotiationId },
              { rateOfferId: { in: offerIds } },
            ],
            ...(input.dateRange && {
              createdAt: {
                gte: input.dateRange.start,
                lte: input.dateRange.end,
              },
            }),
          },
          include: { actor: true },
        });
        events.push(...actions.map((a) => ({ type: "action", ...a })));
      }

      // Get offers
      if (!input.eventTypes || input.eventTypes.includes("offer")) {
        const offers = await ctx.prisma.rateOffer.findMany({
          where: {
            negotiationId: input.negotiationId,
            ...(input.dateRange && {
              createdAt: {
                gte: input.dateRange.start,
                lte: input.dateRange.end,
              },
            }),
          },
        });
        events.push(...offers.map((o) => ({ type: "offer", ...o })));
      }

      // Sort chronologically and apply pagination
      const sortedEvents = events
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
        .slice(input.offset, input.offset + input.limit);

      return sortedEvents;
    }),
});
```

### 4.5 Create offer expiration automation

**File:** `packages/api-medical/src/jobs/negotiations/automation.ts`
**Description:** Implement automated offer expiration checking and status updates with scheduled jobs

**Implementation:**

```typescript
export async function processExpiredOffers() {
  const expiredOffers = await prisma.rateOffer.findMany({
    where: {
      status: "PENDING",
      expiresAt: { lte: new Date() },
    },
    include: { negotiation: true },
  });

  for (const offer of expiredOffers) {
    await prisma.$transaction(async (tx) => {
      // Update offer status
      await tx.rateOffer.update({
        where: { id: offer.id },
        data: { status: "EXPIRED" },
      });

      // Log expiration action
      await performAction({
        type: ActionType.EXPIRE_RATE_OFFER,
        resourceType: ResourceType.RATE_OFFER,
        resourceId: offer.id,
        actorId: "system",
        organizationId: offer.negotiation.organizationId,
        providerId: offer.negotiation.providerId,
      });
    });
  }
}
```

### 4.6 Implement stale negotiation detection

**File:** `packages/api-medical/src/jobs/negotiations/staleDetection.ts`
**Description:** Create system to identify and flag negotiations without recent activity for follow-up

**Implementation:**

```typescript
export async function detectStaleNegotiations() {
  const staleThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

  const staleNegotiations = await prisma.rateNegotiation.findMany({
    where: {
      status: "ACTIVE",
      updatedAt: { lte: staleThreshold },
    },
    include: {
      offers: { orderBy: { createdAt: "desc" }, take: 1 },
      job: true,
      provider: { include: { person: true } },
      organization: true,
    },
  });

  for (const negotiation of staleNegotiations) {
    // Send reminder notifications
    await performAction({
      type: ActionType.STALE_NEGOTIATION_REMINDER,
      resourceType: ResourceType.RATE_NEGOTIATION,
      resourceId: negotiation.id,
      actorId: "system",
      organizationId: negotiation.organizationId,
      providerId: negotiation.providerId,
      metadata: {
        daysSinceLastActivity: Math.floor(
          (Date.now() - negotiation.updatedAt.getTime()) /
            (24 * 60 * 60 * 1000),
        ),
      },
    });
  }
}
```

### 4.7 Add rate negotiation reporting

**File:** `packages/api-medical/src/router/admin/reports.ts`
**Description:** Implement analytics and reporting features for negotiation completion rates, average resolution time, and rate distributions

**Reporting Features:**

```typescript
export const negotiationReportsRouter = createTRPCRouter({
  getCompletionRates: adminProcedure
    .input(
      z.object({
        dateRange: z.object({
          start: z.date(),
          end: z.date(),
        }),
        organizationId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const negotiations = await ctx.prisma.rateNegotiation.findMany({
        where: {
          createdAt: {
            gte: input.dateRange.start,
            lte: input.dateRange.end,
          },
          ...(input.organizationId && { organizationId: input.organizationId }),
        },
      });

      const total = negotiations.length;
      const completed = negotiations.filter(
        (n) => n.status === "FINALIZED",
      ).length;
      const cancelled = negotiations.filter(
        (n) => n.status === "CANCELLED",
      ).length;
      const expired = negotiations.filter((n) => n.status === "EXPIRED").length;

      return {
        total,
        completionRate: total > 0 ? (completed / total) * 100 : 0,
        cancellationRate: total > 0 ? (cancelled / total) * 100 : 0,
        expirationRate: total > 0 ? (expired / total) * 100 : 0,
      };
    }),

  getAverageResolutionTime: adminProcedure
    .input(
      z.object({
        dateRange: z.object({
          start: z.date(),
          end: z.date(),
        }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const completedNegotiations = await ctx.prisma.rateNegotiation.findMany({
        where: {
          status: "FINALIZED",
          createdAt: {
            gte: input.dateRange.start,
            lte: input.dateRange.end,
          },
        },
      });

      if (completedNegotiations.length === 0) return { averageHours: 0 };

      const totalHours = completedNegotiations.reduce((sum, negotiation) => {
        const hours =
          (negotiation.updatedAt.getTime() - negotiation.createdAt.getTime()) /
          (1000 * 60 * 60);
        return sum + hours;
      }, 0);

      return {
        averageHours: totalHours / completedNegotiations.length,
        count: completedNegotiations.length,
      };
    }),

  getRateDistribution: adminProcedure
    .input(
      z.object({
        dateRange: z.object({
          start: z.date(),
          end: z.date(),
        }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const negotiations = await ctx.prisma.rateNegotiation.findMany({
        where: {
          status: "FINALIZED",
          createdAt: {
            gte: input.dateRange.start,
            lte: input.dateRange.end,
          },
        },
        include: { job: true },
      });

      // Calculate rate distribution statistics
      const rates = negotiations.map((n) => n.finalAgreedRate!);
      const distribution = {
        min: Math.min(...rates),
        max: Math.max(...rates),
        average: rates.reduce((sum, rate) => sum + rate, 0) / rates.length,
        median: rates.sort()[Math.floor(rates.length / 2)],
      };

      return distribution;
    }),
});
```

### 4.8 Optimize database queries and performance

**File:** Database optimization tasks
**Description:** Add proper indexes, optimize complex queries, and implement caching where appropriate

**Optimization Tasks:**

1. **Add Database Indexes:**

   ```sql
   -- Additional indexes for performance
   CREATE INDEX idx_rate_negotiation_status_created ON rate_negotiation(status, created_at);
   CREATE INDEX idx_rate_offer_status_expires ON rate_offer(status, expires_at);
   CREATE INDEX idx_rate_negotiation_provider_job ON rate_negotiation(provider_id, job_id);
   ```

2. **Query Optimization:**

   - Use proper select statements to limit data transfer
   - Implement pagination for large result sets
   - Add database connection pooling
   - Use read replicas for reporting queries

3. **Caching Strategy:**

   ```typescript
   // Cache frequently accessed negotiation data
   export async function getCachedNegotiation(negotiationId: string) {
     const cacheKey = `negotiation:${negotiationId}`;
     let negotiation = await redis.get(cacheKey);

     if (!negotiation) {
       negotiation = await prisma.rateNegotiation.findUnique({
         where: { id: negotiationId },
         include: { offers: true, job: true },
       });
       await redis.setex(cacheKey, 300, JSON.stringify(negotiation)); // 5 min cache
     }

     return JSON.parse(negotiation);
   }
   ```

### 4.9-4.12 Additional Phase 4 Tasks

**4.9 Create rate negotiation configuration system**

- File: `packages/api-medical/src/lib/config/negotiations.ts`
- Implement system-wide configuration for default expiration times, rate limits, and negotiation rules

**4.10 Implement negotiation search and filtering**

- File: `packages/api-medical/src/router/negotiations/search.ts`
- Create advanced search and filtering capabilities for negotiations by status, date, rate range, etc.

**4.11 Create negotiation export functionality**

- File: `packages/api-medical/src/router/negotiations/export.ts`
- Implement data export features for negotiations and offers in CSV/Excel format for reporting

**4.12 Implement rate trend analysis**

- File: `packages/api-medical/src/router/admin/analytics.ts`
- Create analytics to track rate trends, successful negotiation patterns, and market insights

---

## 🧪 Phase 5: Testing & Validation (9 tasks)

_Estimated: 2-3 weeks_

### 5.1 Create unit tests for database models

**File:** `packages/api-medical/src/__tests__/models/rateNegotiation.test.ts`
**Description:** Write comprehensive unit tests for RateNegotiation and RateOffer models, including validation and relationship tests

**Test Coverage:**

```typescript
describe("RateNegotiation Model", () => {
  test("should create negotiation with valid data", async () => {
    const negotiation = await prisma.rateNegotiation.create({
      data: {
        jobId: "test-job-id",
        providerId: "test-provider-id",
        organizationId: "test-org-id",
        minRate: 50,
        maxRate: 100,
      },
    });

    expect(negotiation.status).toBe("ACTIVE");
    expect(negotiation.isOpen).toBe(true);
  });

  test("should enforce unique constraint on job-provider pair", async () => {
    // Test duplicate negotiation creation
    await expect(
      prisma.rateNegotiation.create({
        data: {
          jobId: "existing-job-id",
          providerId: "existing-provider-id",
          organizationId: "test-org-id",
          minRate: 50,
          maxRate: 100,
        },
      }),
    ).rejects.toThrow();
  });

  test("should validate rate boundaries", async () => {
    // Test invalid rate ranges
    await expect(
      prisma.rateNegotiation.create({
        data: {
          jobId: "test-job-id",
          providerId: "test-provider-id",
          organizationId: "test-org-id",
          minRate: 100,
          maxRate: 50, // Invalid: max < min
        },
      }),
    ).rejects.toThrow();
  });
});

describe("RateOffer Model", () => {
  test("should create offer with valid data", async () => {
    const offer = await prisma.rateOffer.create({
      data: {
        negotiationId: "test-negotiation-id",
        proposedRate: 75,
        madeBy: "PROVIDER",
      },
    });

    expect(offer.status).toBe("PENDING");
    expect(offer.type).toBe("INITIAL");
  });

  test("should handle offer expiration", async () => {
    const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000);
    const offer = await prisma.rateOffer.create({
      data: {
        negotiationId: "test-negotiation-id",
        proposedRate: 75,
        madeBy: "PROVIDER",
        expiresAt: futureDate,
      },
    });

    expect(offer.expiresAt).toEqual(futureDate);
  });
});
```

### 5.2 Create API endpoint integration tests

**File:** `packages/api-medical/src/__tests__/integration/negotiations.test.ts`
**Description:** Write integration tests for all negotiation API endpoints covering success cases, error handling, and edge cases

**Test Scenarios:**

```typescript
describe("Negotiations API Integration", () => {
  describe("POST /negotiations/start", () => {
    test("should start negotiation successfully", async () => {
      const response = await request(app)
        .post("/api/trpc/rateNegotiations.start")
        .set("Authorization", `Bearer ${providerToken}`)
        .send({
          jobId: "test-job-id",
          initialOfferRate: 75,
          message: "Initial offer",
        });

      expect(response.status).toBe(200);
      expect(response.body.result.data.status).toBe("ACTIVE");
    });

    test("should reject duplicate negotiation", async () => {
      // First negotiation
      await request(app)
        .post("/api/trpc/rateNegotiations.start")
        .set("Authorization", `Bearer ${providerToken}`)
        .send({ jobId: "test-job-id" });

      // Duplicate attempt
      const response = await request(app)
        .post("/api/trpc/rateNegotiations.start")
        .set("Authorization", `Bearer ${providerToken}`)
        .send({ jobId: "test-job-id" });

      expect(response.status).toBe(400);
      expect(response.body.error.message).toContain("already exists");
    });

    test("should reject unauthorized user", async () => {
      const response = await request(app)
        .post("/api/trpc/rateNegotiations.start")
        .send({ jobId: "test-job-id" });

      expect(response.status).toBe(401);
    });
  });

  describe("POST /negotiations/offers/submit", () => {
    test("should submit offer successfully", async () => {
      const response = await request(app)
        .post("/api/trpc/rateNegotiations.offers.submit")
        .set("Authorization", `Bearer ${organizationToken}`)
        .send({
          negotiationId: "test-negotiation-id",
          proposedRate: 80,
          message: "Counter offer",
        });

      expect(response.status).toBe(200);
      expect(response.body.result.data.proposedRate).toBe(80);
    });

    test("should reject rate outside boundaries", async () => {
      const response = await request(app)
        .post("/api/trpc/rateNegotiations.offers.submit")
        .set("Authorization", `Bearer ${organizationToken}`)
        .send({
          negotiationId: "test-negotiation-id",
          proposedRate: 200, // Outside max range
        });

      expect(response.status).toBe(400);
      expect(response.body.error.message).toContain("exceeds maximum");
    });
  });
});
```

### 5.3 Test business rule validation

**File:** `packages/api-medical/src/__tests__/validation/businessRules.test.ts`
**Description:** Create tests for rate validation, permission checking, workflow rules, and state transitions

**Test Cases:**

```typescript
describe("Business Rule Validation", () => {
  describe("Rate Validation", () => {
    test("should validate rate within bounds", () => {
      expect(() => validateRateWithinBounds(75, 50, 100)).not.toThrow();
      expect(() => validateRateWithinBounds(25, 50, 100)).toThrow(
        "below minimum",
      );
      expect(() => validateRateWithinBounds(150, 50, 100)).toThrow(
        "exceeds maximum",
      );
    });

    test("should calculate rate deviation correctly", () => {
      const deviation = calculateRateDeviation(75, 50, 100);
      expect(deviation).toBe(0); // 75 is midpoint of 50-100
    });
  });

  describe("State Transitions", () => {
    test("should allow valid state transitions", () => {
      expect(validateStateTransition("ACTIVE", "FINALIZED")).toBe(true);
      expect(validateStateTransition("ACTIVE", "CANCELLED")).toBe(true);
      expect(validateStateTransition("FINALIZED", "ACTIVE")).toBe(false);
    });
  });

  describe("Permission Validation", () => {
    test("should validate negotiation permissions", async () => {
      const mockCtx = {
        user: { providerId: "test-provider-id", role: "PROVIDER" },
        prisma: mockPrisma,
      };

      await expect(
        validateNegotiationPermissions(mockCtx, "test-negotiation-id", "read"),
      ).resolves.not.toThrow();
    });
  });
});
```

### 5.4 Test notification system integration

**File:** `packages/api-medical/src/__tests__/notifications/rateNegotiations.test.ts`
**Description:** Verify all notification scenarios work correctly with proper message content and delivery

### 5.5 Test concurrent negotiation scenarios

**File:** `packages/api-medical/src/__tests__/concurrency/negotiations.test.ts`
**Description:** Test system behavior under concurrent access, multiple offers, and race condition scenarios

### 5.6 Performance testing and optimization

**File:** `packages/api-medical/src/__tests__/performance/negotiations.test.ts`
**Description:** Load test the system with multiple negotiations and optimize for performance bottlenecks

### 5.7 End-to-end workflow testing

**File:** `packages/api-medical/src/__tests__/e2e/negotiationWorkflow.test.ts`
**Description:** Test complete negotiation workflows from start to finalization including all user interactions

### 5.8 Security and permission testing

**File:** `packages/api-medical/src/__tests__/security/permissions.test.ts`
**Description:** Verify all security boundaries, permission checks, and data access controls work correctly

### 5.9 Data migration and rollback testing

**File:** `packages/api-medical/src/__tests__/migration/rateNegotiations.test.ts`
**Description:** Test database migration process and ensure rollback procedures work if needed

---

## 📋 Implementation Summary

**Total Tasks:** 54 granular tasks across 5 phases
**Estimated Timeline:** 10-15 weeks
**Key Deliverables:**

- Complete rate negotiation system
- Full API implementation
- Notification and action integration
- Admin dashboard and reporting
- Comprehensive testing suite

**Success Criteria:**

- All tests passing
- Performance benchmarks met
- Security audit completed
- User acceptance testing passed
- Production deployment ready
